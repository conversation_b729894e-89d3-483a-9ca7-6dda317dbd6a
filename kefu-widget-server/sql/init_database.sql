-- 客服系统数据库完整初始化脚本
-- 创建时间: 2025-05-27
-- 描述: 包含所有表结构和初始数据的完整脚本

-- 创建数据库
CREATE DATABASE IF NOT EXISTS `kefu_server` DEFAULT CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;

USE `kefu_server`;

-- 设置外键检查
SET FOREIGN_KEY_CHECKS = 0;

-- ========================================
-- 基础表结构
-- ========================================

-- 公司表
CREATE TABLE IF NOT EXISTS `ks_company` (
  `id` bigint(20) unsigned NOT NULL AUTO_INCREMENT COMMENT '公司ID',
  `name` varchar(100) NOT NULL COMMENT '公司名称',
  `introduce` varchar(512) DEFAULT NULL COMMENT '公司介绍',
  `logo` varchar(255) DEFAULT NULL COMMENT '公司LOGO',
  `address` varchar(255) DEFAULT NULL COMMENT '公司地址',
  `telphone` varchar(20) DEFAULT NULL COMMENT '公司电话',
  `owner_id` bigint(32) unsigned NOT NULL COMMENT '公司拥有者ID',
  `status` tinyint(1) NOT NULL DEFAULT '1' COMMENT '状态：1-正常，0-禁用',
  `expire_time` datetime DEFAULT NULL COMMENT '过期时间',
  `created_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updated_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`),
  KEY `idx_name` (`name`),
  KEY `idx_status` (`status`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='公司表';

-- 部门表
CREATE TABLE IF NOT EXISTS `ks_department` (
  `id` bigint(20) unsigned NOT NULL AUTO_INCREMENT COMMENT '部门ID',
  `company_id` bigint(20) unsigned NOT NULL COMMENT '公司ID',
  `parent_id` bigint(20) unsigned DEFAULT 0 COMMENT '上级部门ID，0表示顶级部门',
  `name` varchar(100) NOT NULL COMMENT '部门名称',
  `description` text COMMENT '部门描述',
  `manager_id` bigint(20) unsigned DEFAULT 0 COMMENT '部门经理ID',
  `sort` int(11) DEFAULT 0 COMMENT '排序',
  `status` tinyint(1) DEFAULT 1 COMMENT '状态：1-正常，0-禁用',
  `created_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updated_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `deleted_at` datetime DEFAULT NULL COMMENT '删除时间',
  PRIMARY KEY (`id`),
  KEY `idx_company_id` (`company_id`),
  KEY `idx_parent_id` (`parent_id`),
  KEY `idx_manager_id` (`manager_id`),
  KEY `idx_status` (`status`),
  KEY `idx_deleted_at` (`deleted_at`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='部门表';

-- 角色表
CREATE TABLE IF NOT EXISTS `ks_role` (
  `id` bigint(20) unsigned NOT NULL AUTO_INCREMENT COMMENT '角色ID',
  `company_id` bigint(20) unsigned NOT NULL COMMENT '公司ID',
  `name` varchar(50) NOT NULL COMMENT '角色名称',
  `description` text COMMENT '角色描述',
  `type` tinyint(1) DEFAULT 2 COMMENT '角色类型：1-系统角色，2-自定义角色',
  `permissions` text COMMENT '权限列表，JSON格式',
  `status` tinyint(1) DEFAULT 1 COMMENT '状态：1-正常，0-禁用',
  `created_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updated_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `deleted_at` datetime DEFAULT NULL COMMENT '删除时间',
  PRIMARY KEY (`id`),
  KEY `idx_company_id` (`company_id`),
  KEY `idx_type` (`type`),
  KEY `idx_status` (`status`),
  KEY `idx_deleted_at` (`deleted_at`),
  UNIQUE KEY `uk_company_name` (`company_id`, `name`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='角色表';

-- 员工表
CREATE TABLE IF NOT EXISTS `ks_employee` (
  `id` bigint(20) unsigned NOT NULL AUTO_INCREMENT COMMENT '员工ID',
  `company_id` bigint(20) unsigned NOT NULL COMMENT '公司ID',
  `department_id` bigint(20) unsigned NOT NULL COMMENT '部门ID',
  `employee_no` varchar(50) DEFAULT NULL COMMENT '员工工号',
  `username` varchar(50) NOT NULL COMMENT '用户名',
  `password` varchar(255) NOT NULL COMMENT '密码',
  `real_name` varchar(50) NOT NULL COMMENT '真实姓名',
  `nickname` varchar(50) DEFAULT NULL COMMENT '昵称',
  `avatar` varchar(255) DEFAULT NULL COMMENT '头像',
  `gender` tinyint(1) DEFAULT 0 COMMENT '性别：1-男，2-女，0-未知',
  `birthday` date DEFAULT NULL COMMENT '生日',
  `phone` varchar(20) DEFAULT NULL COMMENT '手机号',
  `email` varchar(100) DEFAULT NULL COMMENT '邮箱',
  `id_card` varchar(20) DEFAULT NULL COMMENT '身份证号',
  `address` varchar(255) DEFAULT NULL COMMENT '地址',
  `entry_date` date DEFAULT NULL COMMENT '入职日期',
  `leave_date` date DEFAULT NULL COMMENT '离职日期',
  `status` tinyint(1) DEFAULT 1 COMMENT '状态：1-在职，2-离职，0-禁用',
  `remark` text COMMENT '备注',
  `created_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updated_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `deleted_at` datetime DEFAULT NULL COMMENT '删除时间',
  PRIMARY KEY (`id`),
  KEY `idx_company_id` (`company_id`),
  KEY `idx_department_id` (`department_id`),
  KEY `idx_phone` (`phone`),
  KEY `idx_email` (`email`),
  KEY `idx_status` (`status`),
  KEY `idx_deleted_at` (`deleted_at`),
  UNIQUE KEY `uk_company_username` (`company_id`, `username`),
  UNIQUE KEY `uk_company_employee_no` (`company_id`, `employee_no`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='员工表';

-- 员工角色关联表
CREATE TABLE IF NOT EXISTS `ks_employee_role` (
  `id` bigint(20) unsigned NOT NULL AUTO_INCREMENT COMMENT '关联ID',
  `employee_id` bigint(20) unsigned NOT NULL COMMENT '员工ID',
  `role_id` bigint(20) unsigned NOT NULL COMMENT '角色ID',
  `created_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  PRIMARY KEY (`id`),
  KEY `idx_employee_id` (`employee_id`),
  KEY `idx_role_id` (`role_id`),
  UNIQUE KEY `uk_employee_role` (`employee_id`, `role_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='员工角色关联表';

-- 座席表
CREATE TABLE IF NOT EXISTS `ks_seat` (
  `id` bigint(20) unsigned NOT NULL AUTO_INCREMENT COMMENT '座席ID',
  `company_id` bigint(20) unsigned NOT NULL COMMENT '公司ID',
  `name` varchar(100) NOT NULL COMMENT '座席名称',
  `max_duration` int(11) NOT NULL DEFAULT '0' COMMENT '最大时长(分钟)',
  `used_duration` int(11) NOT NULL DEFAULT '0' COMMENT '已使用时长(分钟)',
  `status` tinyint(1) NOT NULL DEFAULT '1' COMMENT '状态：1-正常，0-禁用',
  `expire_time` datetime DEFAULT NULL COMMENT '过期时间',
  `created_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updated_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`),
  KEY `idx_company_id` (`company_id`),
  KEY `idx_status` (`status`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='座席表';

-- 网站站点表
CREATE TABLE IF NOT EXISTS `ks_site` (
  `id` bigint(20) unsigned NOT NULL AUTO_INCREMENT COMMENT '站点ID',
  `company_id` bigint(20) unsigned NOT NULL COMMENT '公司ID',
  `name` varchar(100) NOT NULL COMMENT '站点名称',
  `domain` varchar(255) NOT NULL COMMENT '站点域名',
  `site_key` varchar(32) NOT NULL COMMENT '站点密钥',
  `status` tinyint(1) NOT NULL DEFAULT '1' COMMENT '状态：1-正常，0-禁用',
  `created_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updated_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_site_key` (`site_key`),
  KEY `idx_company_id` (`company_id`),
  KEY `idx_domain` (`domain`),
  KEY `idx_status` (`status`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='网站站点表';

-- 访客表
CREATE TABLE IF NOT EXISTS `ks_visitor` (
  `id` bigint(20) unsigned NOT NULL AUTO_INCREMENT COMMENT '访客ID',
  `site_id` bigint(20) unsigned NOT NULL COMMENT '站点ID',
  `visitor_id` varchar(64) NOT NULL COMMENT '访客唯一标识',
  `nickname` varchar(50) DEFAULT NULL COMMENT '访客昵称',
  `avatar` varchar(255) DEFAULT NULL COMMENT '访客头像',
  `email` varchar(100) DEFAULT NULL COMMENT '访客邮箱',
  `phone` varchar(20) DEFAULT NULL COMMENT '访客电话',
  `ip_address` varchar(45) DEFAULT NULL COMMENT 'IP地址',
  `user_agent` text COMMENT '用户代理',
  `referrer` varchar(500) DEFAULT NULL COMMENT '来源页面',
  `location` varchar(100) DEFAULT NULL COMMENT '地理位置',
  `first_visit` datetime DEFAULT NULL COMMENT '首次访问时间',
  `last_visit` datetime DEFAULT NULL COMMENT '最后访问时间',
  `visit_count` int(11) DEFAULT 0 COMMENT '访问次数',
  `status` tinyint(1) DEFAULT 1 COMMENT '状态：1-正常，0-禁用',
  `created_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updated_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_site_visitor` (`site_id`, `visitor_id`),
  KEY `idx_ip_address` (`ip_address`),
  KEY `idx_status` (`status`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='访客表';

-- 聊天消息表
CREATE TABLE IF NOT EXISTS `ks_chat_message` (
  `id` bigint(20) unsigned NOT NULL AUTO_INCREMENT COMMENT '消息ID',
  `site_id` bigint(20) unsigned NOT NULL COMMENT '站点ID',
  `session_id` varchar(64) NOT NULL COMMENT '会话ID',
  `visitor_id` varchar(64) NOT NULL COMMENT '访客ID',
  `seat_id` bigint(20) unsigned DEFAULT 0 COMMENT '座席ID',
  `customer_service_id` bigint(20) unsigned DEFAULT 0 COMMENT '客服ID',
  `message_type` tinyint(1) NOT NULL DEFAULT 1 COMMENT '消息类型：1-文本，2-图片，3-文件，4-语音，5-视频',
  `sender_type` tinyint(1) NOT NULL COMMENT '发送者类型：1-访客，2-客服，3-系统',
  `content` text NOT NULL COMMENT '消息内容',
  `file_url` varchar(500) DEFAULT NULL COMMENT '文件URL',
  `file_name` varchar(255) DEFAULT NULL COMMENT '文件名',
  `file_size` bigint(20) DEFAULT 0 COMMENT '文件大小',
  `is_read` tinyint(1) DEFAULT 0 COMMENT '是否已读：1-已读，0-未读',
  `read_time` datetime DEFAULT NULL COMMENT '阅读时间',
  `created_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  PRIMARY KEY (`id`),
  KEY `idx_site_id` (`site_id`),
  KEY `idx_session_id` (`session_id`),
  KEY `idx_visitor_id` (`visitor_id`),
  KEY `idx_customer_service_id` (`customer_service_id`),
  KEY `idx_created_at` (`created_at`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='聊天消息表';

-- ========================================
-- 初始数据插入
-- ========================================

-- 插入默认公司数据
INSERT IGNORE INTO `ks_company` (`id`, `name`, `introduce`, `logo`, `address`, `telphone`, `owner_id`, `status`, `expire_time`)
VALUES (1, '示例科技有限公司', '专业的客服系统解决方案提供商', '', '北京市朝阳区示例大厦', '************', 1, 1, '2025-12-31 23:59:59');

-- 插入默认部门数据
INSERT IGNORE INTO `ks_department` (`id`, `company_id`, `parent_id`, `name`, `code`, `description`, `manager_id`, `level`, `sort`, `status`)
VALUES
(1, 1, 0, '总经理办公室', 'CEO_OFFICE', '公司最高管理层', 0, 1, 1, 1),
(2, 1, 0, '技术部', 'TECH_DEPT', '负责产品研发和技术支持', 0, 1, 2, 1),
(3, 1, 0, '市场部', 'MARKET_DEPT', '负责市场推广和销售', 0, 1, 3, 1),
(4, 1, 0, '客服部', 'SERVICE_DEPT', '负责客户服务和支持', 0, 1, 4, 1),
(5, 1, 0, '人事部', 'HR_DEPT', '负责人力资源管理', 0, 1, 5, 1),
(6, 1, 0, '财务部', 'FINANCE_DEPT', '负责财务管理', 0, 1, 6, 1),
(7, 1, 2, '前端开发组', 'FRONTEND_GROUP', '负责前端界面开发', 0, 2, 1, 1),
(8, 1, 2, '后端开发组', 'BACKEND_GROUP', '负责后端服务开发', 0, 2, 2, 1),
(9, 1, 2, '测试组', 'TEST_GROUP', '负责产品测试', 0, 2, 3, 1),
(10, 1, 4, '在线客服组', 'ONLINE_SERVICE_GROUP', '负责在线客户服务', 0, 2, 1, 1),
(11, 1, 4, '电话客服组', 'PHONE_SERVICE_GROUP', '负责电话客户服务', 0, 2, 2, 1);

-- 插入默认角色数据
INSERT IGNORE INTO `ks_role` (`id`, `company_id`, `name`, `code`, `description`, `type`, `level`, `permissions`, `status`)
VALUES
(1, 1, '超级管理员', 'SUPER_ADMIN', '拥有系统所有权限', 1, 1, '["*"]', 1),
(2, 1, '总经理', 'CEO', '公司最高管理者', 2, 2, '["company_manage", "dept_manage", "employee_manage", "role_manage", "report_view"]', 1),
(3, 1, '部门经理', 'DEPT_MANAGER', '部门管理者', 2, 3, '["dept_view", "employee_manage", "report_view"]', 1),
(4, 1, '技术经理', 'TECH_MANAGER', '技术部门管理者', 2, 3, '["tech_manage", "employee_manage", "system_config"]', 1),
(5, 1, '客服经理', 'SERVICE_MANAGER', '客服部门管理者', 2, 3, '["service_manage", "employee_manage", "chat_monitor"]', 1),
(6, 1, '高级工程师', 'SENIOR_ENGINEER', '高级技术人员', 2, 4, '["tech_develop", "code_review", "system_config"]', 1),
(7, 1, '工程师', 'ENGINEER', '普通技术人员', 2, 5, '["tech_develop"]', 1),
(8, 1, '高级客服', 'SENIOR_SERVICE', '高级客服人员', 2, 4, '["chat_service", "service_train", "quality_check"]', 1),
(9, 1, '客服专员', 'SERVICE_SPECIALIST', '普通客服人员', 2, 5, '["chat_service"]', 1),
(10, 1, '人事专员', 'HR_SPECIALIST', '人事管理人员', 2, 5, '["employee_info", "attendance_manage"]', 1),
(11, 1, '财务专员', 'FINANCE_SPECIALIST', '财务管理人员', 2, 5, '["finance_manage", "report_view"]', 1),
(12, 1, '市场专员', 'MARKET_SPECIALIST', '市场推广人员', 2, 5, '["market_manage", "customer_manage"]', 1);

-- 插入默认员工数据（密码为 123456 的MD5值）
INSERT IGNORE INTO `ks_employee` (`id`, `company_id`, `department_id`, `employee_no`, `username`, `password`, `real_name`, `nickname`, `phone`, `email`, `position`, `level`, `salary`, `status`)
VALUES
(1, 1, 1, 'CEO001', 'admin', 'e10adc3949ba59abbe56e057f20f883e', '张总', '张总', '13800000001', '<EMAIL>', '总经理', 'CEO', 50000.00, 1),
(2, 1, 2, 'TECH001', 'tech_manager', 'e10adc3949ba59abbe56e057f20f883e', '李技术', '老李', '13800000002', '<EMAIL>', '技术总监', 'CTO', 30000.00, 1),
(3, 1, 4, 'SER001', 'service_manager', 'e10adc3949ba59abbe56e057f20f883e', '王客服', '小王', '13800000003', '<EMAIL>', '客服经理', 'P7', 20000.00, 1),
(4, 1, 7, 'FE001', 'frontend_dev', 'e10adc3949ba59abbe56e057f20f883e', '赵前端', '小赵', '13800000004', '<EMAIL>', '前端工程师', 'P6', 15000.00, 1),
(5, 1, 8, 'BE001', 'backend_dev', 'e10adc3949ba59abbe56e057f20f883e', '钱后端', '小钱', '13800000005', '<EMAIL>', '后端工程师', 'P6', 15000.00, 1),
(6, 1, 10, 'CS001', 'customer_service1', 'e10adc3949ba59abbe56e057f20f883e', '孙客服', '小孙', '13800000006', '<EMAIL>', '客服专员', 'P5', 8000.00, 1),
(7, 1, 10, 'CS002', 'customer_service2', 'e10adc3949ba59abbe56e057f20f883e', '周客服', '小周', '13800000007', '<EMAIL>', '客服专员', 'P5', 8000.00, 1),
(8, 1, 5, 'HR001', 'hr_specialist', 'e10adc3949ba59abbe56e057f20f883e', '吴人事', '小吴', '13800000008', '<EMAIL>', '人事专员', 'P5', 10000.00, 1),
(9, 1, 6, 'FIN001', 'finance_specialist', 'e10adc3949ba59abbe56e057f20f883e', '郑财务', '小郑', '13800000009', '<EMAIL>', '财务专员', 'P5', 12000.00, 1),
(10, 1, 3, 'MKT001', 'market_specialist', 'e10adc3949ba59abbe56e057f20f883e', '冯市场', '小冯', '13800000010', '<EMAIL>', '市场专员', 'P5', 10000.00, 1);

-- 插入员工角色关联数据
INSERT IGNORE INTO `ks_employee_role` (`employee_id`, `role_id`)
VALUES
(1, 1), -- 张总 -> 超级管理员
(1, 2), -- 张总 -> 总经理
(2, 4), -- 李技术 -> 技术经理
(2, 6), -- 李技术 -> 高级工程师
(3, 5), -- 王客服 -> 客服经理
(3, 8), -- 王客服 -> 高级客服
(4, 7), -- 赵前端 -> 工程师
(5, 7), -- 钱后端 -> 工程师
(6, 9), -- 孙客服 -> 客服专员
(7, 9), -- 周客服 -> 客服专员
(8, 10), -- 吴人事 -> 人事专员
(9, 11), -- 郑财务 -> 财务专员
(10, 12); -- 冯市场 -> 市场专员

-- 更新部门经理信息
UPDATE `ks_department` SET `manager_id` = 1 WHERE `id` = 1; -- 总经理办公室
UPDATE `ks_department` SET `manager_id` = 2 WHERE `id` = 2; -- 技术部
UPDATE `ks_department` SET `manager_id` = 10 WHERE `id` = 3; -- 市场部
UPDATE `ks_department` SET `manager_id` = 3 WHERE `id` = 4; -- 客服部
UPDATE `ks_department` SET `manager_id` = 8 WHERE `id` = 5; -- 人事部
UPDATE `ks_department` SET `manager_id` = 9 WHERE `id` = 6; -- 财务部
UPDATE `ks_department` SET `manager_id` = 4 WHERE `id` = 7; -- 前端开发组
UPDATE `ks_department` SET `manager_id` = 5 WHERE `id` = 8; -- 后端开发组
UPDATE `ks_department` SET `manager_id` = 2 WHERE `id` = 9; -- 测试组
UPDATE `ks_department` SET `manager_id` = 6 WHERE `id` = 10; -- 在线客服组
UPDATE `ks_department` SET `manager_id` = 7 WHERE `id` = 11; -- 电话客服组

-- 插入默认座席数据
INSERT IGNORE INTO `ks_seat` (`id`, `company_id`, `name`, `max_duration`, `used_duration`, `status`, `expire_time`)
VALUES
(1, 1, '座席001', 43200, 0, 1, '2025-12-31 23:59:59'),
(2, 1, '座席002', 43200, 0, 1, '2025-12-31 23:59:59'),
(3, 1, '座席003', 43200, 0, 1, '2025-12-31 23:59:59');

-- 插入默认站点数据
INSERT IGNORE INTO `ks_site` (`id`, `company_id`, `name`, `domain`, `site_key`, `status`)
VALUES
(1, 1, '官方网站', 'www.example.com', 'site_key_001', 1),
(2, 1, '商城网站', 'shop.example.com', 'site_key_002', 1);

-- ========================================
-- 外键约束（可选，根据需要启用）
-- ========================================

-- 恢复外键检查
SET FOREIGN_KEY_CHECKS = 1;

-- 注意：以下外键约束可能会因为数据依赖关系而失败
-- 建议在生产环境中根据实际情况谨慎添加

/*
-- 添加外键约束
ALTER TABLE `ks_department`
  ADD CONSTRAINT `fk_department_company` FOREIGN KEY (`company_id`) REFERENCES `ks_company` (`id`) ON DELETE CASCADE;

ALTER TABLE `ks_employee`
  ADD CONSTRAINT `fk_employee_company` FOREIGN KEY (`company_id`) REFERENCES `ks_company` (`id`) ON DELETE CASCADE,
  ADD CONSTRAINT `fk_employee_department` FOREIGN KEY (`department_id`) REFERENCES `ks_department` (`id`) ON DELETE RESTRICT;

ALTER TABLE `ks_role`
  ADD CONSTRAINT `fk_role_company` FOREIGN KEY (`company_id`) REFERENCES `ks_company` (`id`) ON DELETE CASCADE;

ALTER TABLE `ks_employee_role`
  ADD CONSTRAINT `fk_employee_role_employee` FOREIGN KEY (`employee_id`) REFERENCES `ks_employee` (`id`) ON DELETE CASCADE,
  ADD CONSTRAINT `fk_employee_role_role` FOREIGN KEY (`role_id`) REFERENCES `ks_role` (`id`) ON DELETE CASCADE;

ALTER TABLE `ks_seat`
  ADD CONSTRAINT `fk_seat_company` FOREIGN KEY (`company_id`) REFERENCES `ks_company` (`id`) ON DELETE CASCADE;

ALTER TABLE `ks_site`
  ADD CONSTRAINT `fk_site_company` FOREIGN KEY (`company_id`) REFERENCES `ks_company` (`id`) ON DELETE CASCADE;

ALTER TABLE `ks_visitor`
  ADD CONSTRAINT `fk_visitor_site` FOREIGN KEY (`site_id`) REFERENCES `ks_site` (`id`) ON DELETE CASCADE;

ALTER TABLE `ks_chat_message`
  ADD CONSTRAINT `fk_chat_message_site` FOREIGN KEY (`site_id`) REFERENCES `ks_site` (`id`) ON DELETE CASCADE;
*/
