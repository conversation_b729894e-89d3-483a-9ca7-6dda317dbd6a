-- 客服模块迁移到席位模块
-- 创建时间: 2025-05-29
-- 描述: 将CustomerService表迁移到Agent表，更新所有相关表的外键引用

USE `kefu_server`;

-- 1. 备份原有的客服表数据（如果存在）
CREATE TABLE IF NOT EXISTS `ks_customer_service_backup` AS
SELECT * FROM `ks_customer_service` WHERE 1=0;

-- 如果客服表存在数据，先备份
INSERT IGNORE INTO `ks_customer_service_backup`
SELECT * FROM `ks_customer_service` WHERE EXISTS (SELECT 1 FROM `ks_customer_service` LIMIT 1);

-- 2. 创建新的Agent表（如果不存在）
CREATE TABLE IF NOT EXISTS `ks_agent` (
  `id` bigint(20) unsigned NOT NULL AUTO_INCREMENT COMMENT '席位ID',
  `company_id` bigint(20) unsigned NOT NULL COMMENT '公司ID',
  `site_id` bigint(20) unsigned NOT NULL DEFAULT 1 COMMENT '站点ID',
  `skill_group_id` bigint(20) unsigned NOT NULL DEFAULT 1 COMMENT '技能组ID',
  `employee_id` bigint(20) unsigned NOT NULL DEFAULT 1 COMMENT '员工ID',
  `status` tinyint(1) NOT NULL DEFAULT '3' COMMENT '状态：1-在线，2-忙碌，3-离线，0-禁用',
  `max_concurrent` int(11) NOT NULL DEFAULT '5' COMMENT '最大并发接待数',
  `current_concurrent` int(11) NOT NULL DEFAULT '0' COMMENT '当前接待数',
  `last_login_time` datetime DEFAULT NULL COMMENT '最后登录时间',
  `last_login_ip` varchar(45) DEFAULT NULL COMMENT '最后登录IP',
  `purchase_time` datetime DEFAULT NULL COMMENT '购买时间',
  `expire_time` datetime DEFAULT NULL COMMENT '过期时间',
  `created_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updated_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`),
  KEY `idx_company_id` (`company_id`),
  KEY `idx_site_id` (`site_id`),
  KEY `idx_skill_group_id` (`skill_group_id`),
  KEY `idx_employee_id` (`employee_id`),
  KEY `idx_status` (`status`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='席位表';

-- 3. 迁移客服数据到Agent表（如果客服表存在且有数据）
INSERT IGNORE INTO `ks_agent` (
  `id`, `company_id`, `site_id`, `skill_group_id`, `employee_id`,
  `status`, `max_concurrent`, `current_concurrent`,
  `last_login_time`, `last_login_ip`, `created_at`, `updated_at`
)
SELECT
  `id`, `company_id`,
  COALESCE(`seat_id`, 1) as `site_id`,  -- 使用seat_id作为site_id，如果为空则默认为1
  1 as `skill_group_id`,  -- 默认技能组ID为1
  1 as `employee_id`,     -- 默认员工ID为1，需要后续手动关联
  `status`, `max_concurrent`, `current_concurrent`,
  `last_login_time`, `last_login_ip`, `created_at`, `updated_at`
FROM `ks_customer_service`
WHERE EXISTS (SELECT 1 FROM `ks_customer_service` LIMIT 1);

-- 4. 更新相关表的字段名（如果表存在）

-- 更新聊天消息表
SET @sql = (SELECT IF(
  (SELECT COUNT(*) FROM information_schema.COLUMNS
   WHERE table_schema = DATABASE()
   AND table_name = 'ks_chat_message'
   AND column_name = 'customer_service_id') > 0,
  'ALTER TABLE `ks_chat_message` CHANGE `customer_service_id` `agent_id` bigint(20) unsigned DEFAULT NULL COMMENT "席位ID"',
  'SELECT "ks_chat_message.customer_service_id column does not exist" as message'
));
PREPARE stmt FROM @sql;
EXECUTE stmt;
DEALLOCATE PREPARE stmt;



-- 更新访客历史表
SET @sql = (SELECT IF(
  (SELECT COUNT(*) FROM information_schema.COLUMNS
   WHERE table_schema = DATABASE()
   AND table_name = 'ks_visitor_history'
   AND column_name = 'customer_service_id') > 0,
  'ALTER TABLE `ks_visitor_history` CHANGE `customer_service_id` `agent_id` bigint(20) unsigned DEFAULT NULL COMMENT "席位ID"',
  'SELECT "ks_visitor_history.customer_service_id column does not exist" as message'
));
PREPARE stmt FROM @sql;
EXECUTE stmt;
DEALLOCATE PREPARE stmt;

-- 更新访客会话表
SET @sql = (SELECT IF(
  (SELECT COUNT(*) FROM information_schema.COLUMNS
   WHERE table_schema = DATABASE()
   AND table_name = 'ks_visitor_session'
   AND column_name = 'customer_service_id') > 0,
  'ALTER TABLE `ks_visitor_session` CHANGE `customer_service_id` `agent_id` bigint(20) unsigned DEFAULT NULL COMMENT "分配的席位ID"',
  'SELECT "ks_visitor_session.customer_service_id column does not exist" as message'
));
PREPARE stmt FROM @sql;
EXECUTE stmt;
DEALLOCATE PREPARE stmt;

-- 更新会话转接记录表
SET @sql = (SELECT IF(
  (SELECT COUNT(*) FROM information_schema.COLUMNS
   WHERE table_schema = DATABASE()
   AND table_name = 'ks_session_transfer'
   AND column_name = 'from_customer_service_id') > 0,
  'ALTER TABLE `ks_session_transfer` CHANGE `from_customer_service_id` `from_agent_id` bigint(20) unsigned NOT NULL COMMENT "原席位ID"',
  'SELECT "ks_session_transfer.from_customer_service_id column does not exist" as message'
));
PREPARE stmt FROM @sql;
EXECUTE stmt;
DEALLOCATE PREPARE stmt;

SET @sql = (SELECT IF(
  (SELECT COUNT(*) FROM information_schema.COLUMNS
   WHERE table_schema = DATABASE()
   AND table_name = 'ks_session_transfer'
   AND column_name = 'to_customer_service_id') > 0,
  'ALTER TABLE `ks_session_transfer` CHANGE `to_customer_service_id` `to_agent_id` bigint(20) unsigned NOT NULL COMMENT "目标席位ID"',
  'SELECT "ks_session_transfer.to_customer_service_id column does not exist" as message'
));
PREPARE stmt FROM @sql;
EXECUTE stmt;
DEALLOCATE PREPARE stmt;

-- 更新会话评价表
SET @sql = (SELECT IF(
  (SELECT COUNT(*) FROM information_schema.COLUMNS
   WHERE table_schema = DATABASE()
   AND table_name = 'ks_session_evaluation'
   AND column_name = 'customer_service_id') > 0,
  'ALTER TABLE `ks_session_evaluation` CHANGE `customer_service_id` `agent_id` bigint(20) unsigned NOT NULL COMMENT "席位ID"',
  'SELECT "ks_session_evaluation.customer_service_id column does not exist" as message'
));
PREPARE stmt FROM @sql;
EXECUTE stmt;
DEALLOCATE PREPARE stmt;

-- 更新邀请记录表
SET @sql = (SELECT IF(
  (SELECT COUNT(*) FROM information_schema.COLUMNS
   WHERE table_schema = DATABASE()
   AND table_name = 'ks_invite_record'
   AND column_name = 'customer_service_id') > 0,
  'ALTER TABLE `ks_invite_record` CHANGE `customer_service_id` `agent_id` bigint(20) unsigned NOT NULL COMMENT "席位ID"',
  'SELECT "ks_invite_record.customer_service_id column does not exist" as message'
));
PREPARE stmt FROM @sql;
EXECUTE stmt;
DEALLOCATE PREPARE stmt;

-- 5. 创建新的技能组席位关联表
CREATE TABLE IF NOT EXISTS `ks_skill_group_agent` (
  `id` bigint(20) unsigned NOT NULL AUTO_INCREMENT COMMENT 'ID',
  `skill_group_id` bigint(20) unsigned NOT NULL COMMENT '技能组ID',
  `agent_id` bigint(20) unsigned NOT NULL COMMENT '席位ID',
  `created_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_skill_agent` (`skill_group_id`, `agent_id`),
  KEY `idx_skill_group_id` (`skill_group_id`),
  KEY `idx_agent_id` (`agent_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='技能组席位关联表';

-- 6. 迁移技能组客服关联数据到新表（如果原表存在）
INSERT IGNORE INTO `ks_skill_group_agent` (`skill_group_id`, `agent_id`, `created_at`)
SELECT `skill_group_id`, `customer_service_id`, `created_at`
FROM `ks_skill_group_customer_service`
WHERE EXISTS (SELECT 1 FROM `ks_skill_group_customer_service` LIMIT 1);

-- 7. 重命名原表（保留备份）
SET @sql = (SELECT IF(
  (SELECT COUNT(*) FROM information_schema.TABLES
   WHERE table_schema = DATABASE()
   AND table_name = 'ks_customer_service') > 0,
  'RENAME TABLE `ks_customer_service` TO `ks_customer_service_old`',
  'SELECT "ks_customer_service table does not exist" as message'
));
PREPARE stmt FROM @sql;
EXECUTE stmt;
DEALLOCATE PREPARE stmt;

SET @sql = (SELECT IF(
  (SELECT COUNT(*) FROM information_schema.TABLES
   WHERE table_schema = DATABASE()
   AND table_name = 'ks_skill_group_customer_service') > 0,
  'RENAME TABLE `ks_skill_group_customer_service` TO `ks_skill_group_customer_service_old`',
  'SELECT "ks_skill_group_customer_service table does not exist" as message'
));
PREPARE stmt FROM @sql;
EXECUTE stmt;
DEALLOCATE PREPARE stmt;

-- 8. 输出迁移完成信息
SELECT 'CustomerService模块已成功迁移到Agent模块' as migration_status;
SELECT 'Agent表记录数:' as info, COUNT(*) as count FROM `ks_agent`;
SELECT 'SkillGroupAgent关联表记录数:' as info, COUNT(*) as count FROM `ks_skill_group_agent`;
