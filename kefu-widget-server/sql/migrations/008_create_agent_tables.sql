-- 客服模块迁移到席位模块
-- 创建时间: 2025-05-29
-- 描述: 将CustomerService表迁移到Agent表，更新所有相关表的外键引用

USE `kefu_server`;

-- 1. 创建新的Agent表（如果不存在）
CREATE TABLE IF NOT EXISTS `ks_agent` (
  `id` bigint(20) unsigned NOT NULL AUTO_INCREMENT COMMENT '席位ID',
  `company_id` bigint(20) unsigned NOT NULL COMMENT '公司ID',
  `site_id` bigint(20) unsigned NOT NULL DEFAULT 1 COMMENT '站点ID',
  `skill_group_id` bigint(20) unsigned NOT NULL DEFAULT 1 COMMENT '技能组ID',
  `employee_id` bigint(20) unsigned NOT NULL DEFAULT 1 COMMENT '员工ID',
  `status` tinyint(1) NOT NULL DEFAULT '3' COMMENT '状态：1-在线，2-忙碌，3-离线，0-禁用',
  `max_concurrent` int(11) NOT NULL DEFAULT '5' COMMENT '最大并发接待数',
  `current_concurrent` int(11) NOT NULL DEFAULT '0' COMMENT '当前接待数',
  `last_login_time` datetime DEFAULT NULL COMMENT '最后登录时间',
  `last_login_ip` varchar(45) DEFAULT NULL COMMENT '最后登录IP',
  `purchase_time` datetime DEFAULT NULL COMMENT '购买时间',
  `expire_time` datetime DEFAULT NULL COMMENT '过期时间',
  `created_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updated_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`),
  KEY `idx_company_id` (`company_id`),
  KEY `idx_site_id` (`site_id`),
  KEY `idx_skill_group_id` (`skill_group_id`),
  KEY `idx_employee_id` (`employee_id`),
  KEY `idx_status` (`status`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='席位表';