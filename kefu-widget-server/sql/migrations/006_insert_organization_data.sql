-- 组织架构初始数据插入脚本
-- 创建时间: 2025-05-27
-- 描述: 插入默认的组织架构数据

USE `kefu_server`;

-- 插入默认公司数据（如果不存在）
INSERT IGNORE INTO `ks_company` (`id`, `name`, `introduce`, `logo`, `address`, `telphone`, `owner_id`, `status`, `expire_time`) 
VALUES (1, '示例科技有限公司', '专业的客服系统解决方案提供商', '', '北京市朝阳区示例大厦', '************', 1, 1, '2025-12-31 23:59:59');

-- 插入默认部门数据
INSERT IGNORE INTO `ks_department` (`id`, `company_id`, `parent_id`, `name`, `code`, `description`, `manager_id`, `level`, `sort`, `status`) 
VALUES 
(1, 1, 0, '总经理办公室', 'CEO_OFFICE', '公司最高管理层', 0, 1, 1, 1),
(2, 1, 0, '技术部', 'TECH_DEPT', '负责产品研发和技术支持', 0, 1, 2, 1),
(3, 1, 0, '市场部', 'MARKET_DEPT', '负责市场推广和销售', 0, 1, 3, 1),
(4, 1, 0, '客服部', 'SERVICE_DEPT', '负责客户服务和支持', 0, 1, 4, 1),
(5, 1, 0, '人事部', 'HR_DEPT', '负责人力资源管理', 0, 1, 5, 1),
(6, 1, 0, '财务部', 'FINANCE_DEPT', '负责财务管理', 0, 1, 6, 1),
(7, 1, 2, '前端开发组', 'FRONTEND_GROUP', '负责前端界面开发', 0, 2, 1, 1),
(8, 1, 2, '后端开发组', 'BACKEND_GROUP', '负责后端服务开发', 0, 2, 2, 1),
(9, 1, 2, '测试组', 'TEST_GROUP', '负责产品测试', 0, 2, 3, 1),
(10, 1, 4, '在线客服组', 'ONLINE_SERVICE_GROUP', '负责在线客户服务', 0, 2, 1, 1),
(11, 1, 4, '电话客服组', 'PHONE_SERVICE_GROUP', '负责电话客户服务', 0, 2, 2, 1);

-- 插入默认角色数据
INSERT IGNORE INTO `ks_role` (`id`, `company_id`, `name`, `code`, `description`, `type`, `level`, `permissions`, `status`) 
VALUES 
(1, 1, '超级管理员', 'SUPER_ADMIN', '拥有系统所有权限', 1, 1, '["*"]', 1),
(2, 1, '总经理', 'CEO', '公司最高管理者', 2, 2, '["company_manage", "dept_manage", "employee_manage", "role_manage", "report_view"]', 1),
(3, 1, '部门经理', 'DEPT_MANAGER', '部门管理者', 2, 3, '["dept_view", "employee_manage", "report_view"]', 1),
(4, 1, '技术经理', 'TECH_MANAGER', '技术部门管理者', 2, 3, '["tech_manage", "employee_manage", "system_config"]', 1),
(5, 1, '客服经理', 'SERVICE_MANAGER', '客服部门管理者', 2, 3, '["service_manage", "employee_manage", "chat_monitor"]', 1),
(6, 1, '高级工程师', 'SENIOR_ENGINEER', '高级技术人员', 2, 4, '["tech_develop", "code_review", "system_config"]', 1),
(7, 1, '工程师', 'ENGINEER', '普通技术人员', 2, 5, '["tech_develop"]', 1),
(8, 1, '高级客服', 'SENIOR_SERVICE', '高级客服人员', 2, 4, '["chat_service", "service_train", "quality_check"]', 1),
(9, 1, '客服专员', 'SERVICE_SPECIALIST', '普通客服人员', 2, 5, '["chat_service"]', 1),
(10, 1, '人事专员', 'HR_SPECIALIST', '人事管理人员', 2, 5, '["employee_info", "attendance_manage"]', 1),
(11, 1, '财务专员', 'FINANCE_SPECIALIST', '财务管理人员', 2, 5, '["finance_manage", "report_view"]', 1),
(12, 1, '市场专员', 'MARKET_SPECIALIST', '市场推广人员', 2, 5, '["market_manage", "customer_manage"]', 1);

-- 插入默认员工数据（密码为 123456 的MD5值）
INSERT IGNORE INTO `ks_employee` (`id`, `company_id`, `department_id`, `employee_no`, `username`, `password`, `real_name`, `nickname`, `phone`, `email`, `position`, `level`, `salary`, `status`) 
VALUES 
(1, 1, 1, 'CEO001', 'admin', 'e10adc3949ba59abbe56e057f20f883e', '张总', '张总', '13800000001', '<EMAIL>', '总经理', 'CEO', 50000.00, 1),
(2, 1, 2, 'TECH001', 'tech_manager', 'e10adc3949ba59abbe56e057f20f883e', '李技术', '老李', '13800000002', '<EMAIL>', '技术总监', 'CTO', 30000.00, 1),
(3, 1, 4, 'SER001', 'service_manager', 'e10adc3949ba59abbe56e057f20f883e', '王客服', '小王', '13800000003', '<EMAIL>', '客服经理', 'P7', 20000.00, 1),
(4, 1, 7, 'FE001', 'frontend_dev', 'e10adc3949ba59abbe56e057f20f883e', '赵前端', '小赵', '13800000004', '<EMAIL>', '前端工程师', 'P6', 15000.00, 1),
(5, 1, 8, 'BE001', 'backend_dev', 'e10adc3949ba59abbe56e057f20f883e', '钱后端', '小钱', '13800000005', '<EMAIL>', '后端工程师', 'P6', 15000.00, 1),
(6, 1, 10, 'CS001', 'customer_service1', 'e10adc3949ba59abbe56e057f20f883e', '孙客服', '小孙', '13800000006', '<EMAIL>', '客服专员', 'P5', 8000.00, 1),
(7, 1, 10, 'CS002', 'customer_service2', 'e10adc3949ba59abbe56e057f20f883e', '周客服', '小周', '13800000007', '<EMAIL>', '客服专员', 'P5', 8000.00, 1),
(8, 1, 5, 'HR001', 'hr_specialist', 'e10adc3949ba59abbe56e057f20f883e', '吴人事', '小吴', '13800000008', '<EMAIL>', '人事专员', 'P5', 10000.00, 1),
(9, 1, 6, 'FIN001', 'finance_specialist', 'e10adc3949ba59abbe56e057f20f883e', '郑财务', '小郑', '13800000009', '<EMAIL>', '财务专员', 'P5', 12000.00, 1),
(10, 1, 3, 'MKT001', 'market_specialist', 'e10adc3949ba59abbe56e057f20f883e', '冯市场', '小冯', '13800000010', '<EMAIL>', '市场专员', 'P5', 10000.00, 1);

-- 插入员工角色关联数据
INSERT IGNORE INTO `ks_employee_role` (`employee_id`, `role_id`) 
VALUES 
(1, 1), -- 张总 -> 超级管理员
(1, 2), -- 张总 -> 总经理
(2, 4), -- 李技术 -> 技术经理
(2, 6), -- 李技术 -> 高级工程师
(3, 5), -- 王客服 -> 客服经理
(3, 8), -- 王客服 -> 高级客服
(4, 7), -- 赵前端 -> 工程师
(5, 7), -- 钱后端 -> 工程师
(6, 9), -- 孙客服 -> 客服专员
(7, 9), -- 周客服 -> 客服专员
(8, 10), -- 吴人事 -> 人事专员
(9, 11), -- 郑财务 -> 财务专员
(10, 12); -- 冯市场 -> 市场专员

-- 更新部门经理信息
UPDATE `ks_department` SET `manager_id` = 1 WHERE `id` = 1; -- 总经理办公室
UPDATE `ks_department` SET `manager_id` = 2 WHERE `id` = 2; -- 技术部
UPDATE `ks_department` SET `manager_id` = 10 WHERE `id` = 3; -- 市场部
UPDATE `ks_department` SET `manager_id` = 3 WHERE `id` = 4; -- 客服部
UPDATE `ks_department` SET `manager_id` = 8 WHERE `id` = 5; -- 人事部
UPDATE `ks_department` SET `manager_id` = 9 WHERE `id` = 6; -- 财务部
UPDATE `ks_department` SET `manager_id` = 4 WHERE `id` = 7; -- 前端开发组
UPDATE `ks_department` SET `manager_id` = 5 WHERE `id` = 8; -- 后端开发组
UPDATE `ks_department` SET `manager_id` = 2 WHERE `id` = 9; -- 测试组
UPDATE `ks_department` SET `manager_id` = 6 WHERE `id` = 10; -- 在线客服组
UPDATE `ks_department` SET `manager_id` = 7 WHERE `id` = 11; -- 电话客服组
