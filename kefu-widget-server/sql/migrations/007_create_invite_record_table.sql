-- 邀请访客会话记录表
-- 创建时间: 2025-05-28
-- 描述: 用于记录客服主动邀请访客进行会话的记录

USE `kefu_server`;

-- 邀请访客会话记录表
CREATE TABLE IF NOT EXISTS `ks_invite_record` (
  `id` bigint(20) unsigned NOT NULL AUTO_INCREMENT COMMENT '邀请记录ID',
  `site_id` bigint(20) unsigned NOT NULL COMMENT '站点ID',
  `visitor_id` varchar(64) NOT NULL COMMENT '访客ID',
  `agent_id` bigint(20) unsigned NOT NULL COMMENT '席位ID',
  `invite_type` tinyint(1) NOT NULL DEFAULT '1' COMMENT '邀请类型：1-主动邀请，2-自动邀请',
  `invite_message` text COMMENT '邀请消息内容',
  `invite_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '邀请时间',
  `visitor_response` tinyint(1) DEFAULT NULL COMMENT '访客响应：1-接受，2-拒绝，3-忽略',
  `response_time` datetime DEFAULT NULL COMMENT '响应时间',
  `session_id` varchar(64) DEFAULT NULL COMMENT '会话ID（如果访客接受邀请）',
  `visitor_ip` varchar(45) DEFAULT NULL COMMENT '访客IP地址',
  `visitor_user_agent` text COMMENT '访客浏览器信息',
  `visitor_page_url` varchar(500) DEFAULT NULL COMMENT '访客当前页面URL',
  `visitor_page_title` varchar(200) DEFAULT NULL COMMENT '访客当前页面标题',
  `invite_trigger` varchar(100) DEFAULT NULL COMMENT '邀请触发条件',
  `remark` varchar(500) DEFAULT NULL COMMENT '备注',
  `created_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updated_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`),
  KEY `idx_site_id` (`site_id`),
  KEY `idx_visitor_id` (`visitor_id`),
  KEY `idx_agent_id` (`agent_id`),
  KEY `idx_invite_time` (`invite_time`),
  KEY `idx_invite_type` (`invite_type`),
  KEY `idx_visitor_response` (`visitor_response`),
  KEY `idx_session_id` (`session_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='邀请访客会话记录表';
