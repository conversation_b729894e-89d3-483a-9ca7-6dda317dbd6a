-- 对话消息表
CREATE TABLE IF NOT EXISTS `ks_chat_message` (
  `id` bigint(20) unsigned NOT NULL AUTO_INCREMENT COMMENT '消息ID',
  `site_id` bigint(20) unsigned NOT NULL COMMENT '站点ID',
  `session_id` varchar(64) NOT NULL COMMENT '会话ID',
  `visitor_id` varchar(64) NOT NULL COMMENT '访客ID',
  `agent_id` bigint(20) unsigned DEFAULT NULL COMMENT '席位ID',
  `message_type` tinyint(1) NOT NULL DEFAULT '1' COMMENT '消息类型：1-文本，2-图片，3-文件，4-系统消息',
  `sender_type` tinyint(1) NOT NULL DEFAULT '1' COMMENT '发送者类型：1-访客，2-客服，3-系统',
  `content` text COMMENT '消息内容',
  `file_url` varchar(500) DEFAULT NULL COMMENT '文件URL',
  `file_name` varchar(255) DEFAULT NULL COMMENT '文件名',
  `file_size` bigint(20) DEFAULT NULL COMMENT '文件大小',
  `is_read` tinyint(1) NOT NULL DEFAULT '0' COMMENT '是否已读：1-已读，0-未读',
  `read_time` datetime DEFAULT NULL COMMENT '阅读时间',
  `created_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  PRIMARY KEY (`id`),
  KEY `idx_site_id` (`site_id`),
  KEY `idx_session_id` (`session_id`),
  KEY `idx_visitor_id` (`visitor_id`),
  KEY `idx_agent_id` (`agent_id`),
  KEY `idx_sender_type` (`sender_type`),
  KEY `idx_created_at` (`created_at`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='对话消息表';

-- 技能组表
CREATE TABLE IF NOT EXISTS `ks_skill_group` (
  `id` bigint(20) unsigned NOT NULL AUTO_INCREMENT COMMENT '技能组ID',
  `company_id` bigint(20) unsigned NOT NULL COMMENT '公司ID',
  `name` varchar(100) NOT NULL COMMENT '技能组名称',
  `description` text COMMENT '技能组描述',
  `status` tinyint(1) NOT NULL DEFAULT '1' COMMENT '状态：1-正常，0-禁用',
  `created_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updated_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`),
  KEY `idx_company_id` (`company_id`),
  KEY `idx_name` (`name`),
  KEY `idx_status` (`status`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='技能组表';

-- 席位表
CREATE TABLE IF NOT EXISTS `ks_agent` (
  `id` bigint(20) unsigned NOT NULL AUTO_INCREMENT COMMENT '席位ID',
  `company_id` bigint(20) unsigned NOT NULL COMMENT '公司ID',
  `site_id` bigint(20) unsigned NOT NULL COMMENT '站点ID',
  `skill_group_id` bigint(20) unsigned NOT NULL COMMENT '技能组ID',
  `employee_id` bigint(20) unsigned NOT NULL COMMENT '员工ID',
  `status` tinyint(1) NOT NULL DEFAULT '3' COMMENT '状态：1-在线，2-忙碌，3-离线，0-禁用',
  `max_concurrent` int(11) NOT NULL DEFAULT '5' COMMENT '最大并发接待数',
  `current_concurrent` int(11) NOT NULL DEFAULT '0' COMMENT '当前接待数',
  `last_login_time` datetime DEFAULT NULL COMMENT '最后登录时间',
  `last_login_ip` varchar(45) DEFAULT NULL COMMENT '最后登录IP',
  `purchase_time` datetime DEFAULT NULL COMMENT '购买时间',
  `expire_time` datetime DEFAULT NULL COMMENT '过期时间',
  `created_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updated_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`),
  KEY `idx_company_id` (`company_id`),
  KEY `idx_site_id` (`site_id`),
  KEY `idx_skill_group_id` (`skill_group_id`),
  KEY `idx_employee_id` (`employee_id`),
  KEY `idx_status` (`status`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='席位表';

-- 座席分配表
CREATE TABLE IF NOT EXISTS `ks_seat_allocation` (
  `id` bigint(20) unsigned NOT NULL AUTO_INCREMENT COMMENT 'ID',
  `site_id` bigint(20) unsigned NOT NULL COMMENT '站点ID',
  `seat_id` bigint(20) unsigned NOT NULL COMMENT '座席ID',
  `priority` int(11) NOT NULL DEFAULT '1' COMMENT '优先级',
  `status` tinyint(1) NOT NULL DEFAULT '1' COMMENT '状态：1-正常，0-禁用',
  `created_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updated_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_site_seat` (`site_id`, `seat_id`),
  KEY `idx_site_id` (`site_id`),
  KEY `idx_seat_id` (`seat_id`),
  KEY `idx_priority` (`priority`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='座席分配表';

-- 技能组席位关联表
CREATE TABLE IF NOT EXISTS `ks_skill_group_agent` (
  `id` bigint(20) unsigned NOT NULL AUTO_INCREMENT COMMENT 'ID',
  `skill_group_id` bigint(20) unsigned NOT NULL COMMENT '技能组ID',
  `agent_id` bigint(20) unsigned NOT NULL COMMENT '席位ID',
  `created_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_skill_agent` (`skill_group_id`, `agent_id`),
  KEY `idx_skill_group_id` (`skill_group_id`),
  KEY `idx_agent_id` (`agent_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='技能组席位关联表';
