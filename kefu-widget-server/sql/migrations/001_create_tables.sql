-- 创建数据库
CREATE DATABASE IF NOT EXISTS `kefu_server` DEFAULT CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;

USE `kefu_server`;

-- 座席表
CREATE TABLE IF NOT EXISTS `ks_seat` (
  `id` bigint(20) unsigned NOT NULL AUTO_INCREMENT COMMENT '座席ID',
  `company_id` bigint(20) unsigned NOT NULL COMMENT '公司ID',
  `name` varchar(100) NOT NULL COMMENT '座席名称',
  `max_duration` int(11) NOT NULL DEFAULT '0' COMMENT '最大时长(分钟)',
  `used_duration` int(11) NOT NULL DEFAULT '0' COMMENT '已使用时长(分钟)',
  `status` tinyint(1) NOT NULL DEFAULT '1' COMMENT '状态：1-正常，0-禁用',
  `expire_time` datetime DEFAULT NULL COMMENT '过期时间',
  `created_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updated_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`),
  KEY `idx_company_id` (`company_id`),
  KEY `idx_status` (`status`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='座席表';

-- 网站站点表
CREATE TABLE IF NOT EXISTS `ks_site` (
  `id` bigint(20) unsigned NOT NULL AUTO_INCREMENT COMMENT '站点ID',
  `company_id` bigint(20) unsigned NOT NULL COMMENT '公司ID',
  `name` varchar(100) NOT NULL COMMENT '站点名称',
  `domain` varchar(255) NOT NULL COMMENT '站点域名',
  `site_key` varchar(32) NOT NULL COMMENT '站点密钥',
  `status` tinyint(1) NOT NULL DEFAULT '1' COMMENT '状态：1-正常，0-禁用',
  `created_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updated_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_site_key` (`site_key`),
  KEY `idx_company_id` (`company_id`),
  KEY `idx_domain` (`domain`),
  KEY `idx_status` (`status`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='网站站点表';

-- 网站站点设置表
CREATE TABLE IF NOT EXISTS `ks_site_settings` (
  `id` bigint(20) unsigned NOT NULL AUTO_INCREMENT COMMENT '设置ID',
  `site_id` bigint(20) unsigned NOT NULL COMMENT '站点ID',
  `welcome_message` text COMMENT '欢迎消息',
  `offline_message` text COMMENT '离线消息',
  `auto_reply_enabled` tinyint(1) NOT NULL DEFAULT '0' COMMENT '自动回复启用：1-是，0-否',
  `auto_reply_message` text COMMENT '自动回复消息',
  `working_hours` json COMMENT '工作时间(JSON格式)',
  `created_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updated_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_site_id` (`site_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='网站站点设置表';

-- 邀请弹窗设置表
CREATE TABLE IF NOT EXISTS `ks_invite_popup_settings` (
  `id` bigint(20) unsigned NOT NULL AUTO_INCREMENT COMMENT '设置ID',
  `site_id` bigint(20) unsigned NOT NULL COMMENT '站点ID',
  `enabled` tinyint(1) NOT NULL DEFAULT '0' COMMENT '启用状态：1-启用，0-禁用',
  `title` varchar(255) DEFAULT NULL COMMENT '弹窗标题',
  `content` text COMMENT '弹窗内容',
  `show_delay` int(11) NOT NULL DEFAULT '0' COMMENT '显示延迟(秒)',
  `show_frequency` tinyint(1) NOT NULL DEFAULT '1' COMMENT '显示频率：1-每次访问，2-每天一次，3-每周一次',
  `position` varchar(20) DEFAULT 'center' COMMENT '弹窗位置：center,top,bottom,left,right',
  `style` json COMMENT '样式配置(JSON格式)',
  `created_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updated_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_site_id` (`site_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='邀请弹窗设置表';

-- 咨询图标设置表
CREATE TABLE IF NOT EXISTS `ks_consult_icon_settings` (
  `id` bigint(20) unsigned NOT NULL AUTO_INCREMENT COMMENT '设置ID',
  `site_id` bigint(20) unsigned NOT NULL COMMENT '站点ID',
  `enabled` tinyint(1) NOT NULL DEFAULT '1' COMMENT '启用状态：1-启用，0-禁用',
  `icon_type` varchar(20) DEFAULT 'default' COMMENT '图标类型：default,custom,image',
  `icon_url` varchar(255) DEFAULT NULL COMMENT '图标URL',
  `position` varchar(20) DEFAULT 'bottom-right' COMMENT '位置：bottom-right,bottom-left,top-right,top-left',
  `offset_x` int(11) NOT NULL DEFAULT '20' COMMENT 'X轴偏移',
  `offset_y` int(11) NOT NULL DEFAULT '20' COMMENT 'Y轴偏移',
  `size` varchar(20) DEFAULT 'medium' COMMENT '尺寸：small,medium,large',
  `animation` varchar(50) DEFAULT NULL COMMENT '动画效果',
  `style` json COMMENT '样式配置(JSON格式)',
  `created_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updated_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_site_id` (`site_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='咨询图标设置表';

-- 快捷对话弹窗设置表
CREATE TABLE IF NOT EXISTS `ks_quick_chat_settings` (
  `id` bigint(20) unsigned NOT NULL AUTO_INCREMENT COMMENT '设置ID',
  `site_id` bigint(20) unsigned NOT NULL COMMENT '站点ID',
  `enabled` tinyint(1) NOT NULL DEFAULT '1' COMMENT '启用状态：1-启用，0-禁用',
  `title` varchar(255) DEFAULT NULL COMMENT '弹窗标题',
  `width` int(11) NOT NULL DEFAULT '400' COMMENT '弹窗宽度',
  `height` int(11) NOT NULL DEFAULT '600' COMMENT '弹窗高度',
  `position` varchar(20) DEFAULT 'bottom-right' COMMENT '弹窗位置',
  `show_avatar` tinyint(1) NOT NULL DEFAULT '1' COMMENT '显示头像：1-显示，0-不显示',
  `show_nickname` tinyint(1) NOT NULL DEFAULT '1' COMMENT '显示昵称：1-显示，0-不显示',
  `show_online_status` tinyint(1) NOT NULL DEFAULT '1' COMMENT '显示在线状态：1-显示，0-不显示',
  `quick_replies` json COMMENT '快捷回复(JSON格式)',
  `style` json COMMENT '样式配置(JSON格式)',
  `created_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updated_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_site_id` (`site_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='快捷对话弹窗设置表';

-- 独立对话窗设置表
CREATE TABLE IF NOT EXISTS `ks_independent_chat_settings` (
  `id` bigint(20) unsigned NOT NULL AUTO_INCREMENT COMMENT '设置ID',
  `site_id` bigint(20) unsigned NOT NULL COMMENT '站点ID',
  `enabled` tinyint(1) NOT NULL DEFAULT '0' COMMENT '启用状态：1-启用，0-禁用',
  `window_title` varchar(255) DEFAULT NULL COMMENT '窗口标题',
  `width` int(11) NOT NULL DEFAULT '800' COMMENT '窗口宽度',
  `height` int(11) NOT NULL DEFAULT '600' COMMENT '窗口高度',
  `resizable` tinyint(1) NOT NULL DEFAULT '1' COMMENT '可调整大小：1-可以，0-不可以',
  `show_toolbar` tinyint(1) NOT NULL DEFAULT '1' COMMENT '显示工具栏：1-显示，0-不显示',
  `show_emoji` tinyint(1) NOT NULL DEFAULT '1' COMMENT '显示表情：1-显示，0-不显示',
  `show_file_upload` tinyint(1) NOT NULL DEFAULT '1' COMMENT '显示文件上传：1-显示，0-不显示',
  `show_history` tinyint(1) NOT NULL DEFAULT '1' COMMENT '显示历史记录：1-显示，0-不显示',
  `max_message_length` int(11) NOT NULL DEFAULT '1000' COMMENT '最大消息长度',
  `style` json COMMENT '样式配置(JSON格式)',
  `created_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updated_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_site_id` (`site_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='独立对话窗设置表';

-- 访客留言设置表
CREATE TABLE IF NOT EXISTS `ks_visitor_message_settings` (
  `id` bigint(20) unsigned NOT NULL AUTO_INCREMENT COMMENT '设置ID',
  `site_id` bigint(20) unsigned NOT NULL COMMENT '站点ID',
  `enabled` tinyint(1) NOT NULL DEFAULT '1' COMMENT '启用状态：1-启用，0-禁用',
  `title` varchar(255) DEFAULT NULL COMMENT '留言标题',
  `required_fields` json COMMENT '必填字段(JSON格式)',
  `optional_fields` json COMMENT '可选字段(JSON格式)',
  `max_message_length` int(11) NOT NULL DEFAULT '500' COMMENT '最大留言长度',
  `auto_reply_enabled` tinyint(1) NOT NULL DEFAULT '0' COMMENT '自动回复启用：1-启用，0-禁用',
  `auto_reply_message` text COMMENT '自动回复消息',
  `email_notification` tinyint(1) NOT NULL DEFAULT '0' COMMENT '邮件通知：1-启用，0-禁用',
  `notification_email` varchar(255) DEFAULT NULL COMMENT '通知邮箱',
  `style` json COMMENT '样式配置(JSON格式)',
  `created_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updated_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_site_id` (`site_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='访客留言设置表';
