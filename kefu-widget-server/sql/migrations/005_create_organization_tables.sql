-- 组织架构相关表创建脚本
-- 创建时间: 2025-05-27
-- 描述: 创建部门、员工、角色及关联表

USE `kefu_server`;

-- 公司表
CREATE TABLE IF NOT EXISTS `ks_company` (
  `id` bigint(20) unsigned NOT NULL AUTO_INCREMENT COMMENT '公司ID',
  `name` varchar(100) NOT NULL COMMENT '公司名称',
  `introduce` varchar(512) DEFAULT NULL COMMENT '公司介绍',
  `logo` varchar(255) DEFAULT NULL COMMENT '公司LOGO',
  `address` varchar(255) DEFAULT NULL COMMENT '公司地址',
  `telphone` varchar(20) DEFAULT NULL COMMENT '公司电话',
  `owner_id` bigint(20) unsigned NOT NULL COMMENT '公司拥有者ID',
  `status` tinyint(1) NOT NULL DEFAULT '1' COMMENT '状态：1-正常，0-禁用',
  `expire_time` datetime DEFAULT NULL COMMENT '过期时间',
  `created_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updated_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`),
  KEY `idx_name` (`name`),
  KEY `idx_status` (`status`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='公司表';

-- 部门表
CREATE TABLE IF NOT EXISTS `ks_department` (
  `id` bigint(20) unsigned NOT NULL AUTO_INCREMENT COMMENT '部门ID',
  `company_id` bigint(20) unsigned NOT NULL COMMENT '公司ID',
  `parent_id` bigint(20) unsigned DEFAULT 0 COMMENT '上级部门ID，0表示顶级部门',
  `name` varchar(100) NOT NULL COMMENT '部门名称',
  `description` text COMMENT '部门描述',
  `manager_id` bigint(20) unsigned DEFAULT 0 COMMENT '部门经理ID',
  `sort` int(11) DEFAULT 0 COMMENT '排序',
  `status` tinyint(1) DEFAULT 1 COMMENT '状态：1-正常，0-禁用',
  `created_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updated_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `deleted_at` datetime DEFAULT NULL COMMENT '删除时间',
  PRIMARY KEY (`id`),
  KEY `idx_company_id` (`company_id`),
  KEY `idx_parent_id` (`parent_id`),
  KEY `idx_manager_id` (`manager_id`),
  KEY `idx_status` (`status`),
  KEY `idx_deleted_at` (`deleted_at`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='部门表';

-- 员工表
CREATE TABLE IF NOT EXISTS `ks_employee` (
  `id` bigint(20) unsigned NOT NULL AUTO_INCREMENT COMMENT '员工ID',
  `company_id` bigint(20) unsigned NOT NULL COMMENT '公司ID',
  `department_id` bigint(20) unsigned NOT NULL COMMENT '部门ID',
  `employee_no` varchar(50) DEFAULT NULL COMMENT '员工工号',
  `username` varchar(50) NOT NULL COMMENT '用户名',
  `password` varchar(255) NOT NULL COMMENT '密码',
  `real_name` varchar(50) NOT NULL COMMENT '真实姓名',
  `nickname` varchar(50) DEFAULT NULL COMMENT '昵称',
  `avatar` varchar(255) DEFAULT NULL COMMENT '头像',
  `gender` tinyint(1) DEFAULT 0 COMMENT '性别：1-男，2-女，0-未知',
  `birthday` date DEFAULT NULL COMMENT '生日',
  `phone` varchar(20) DEFAULT NULL COMMENT '手机号',
  `email` varchar(100) DEFAULT NULL COMMENT '邮箱',
  `entry_date` date DEFAULT NULL COMMENT '入职日期',
  `leave_date` date DEFAULT NULL COMMENT '离职日期',
  `status` tinyint(1) DEFAULT 1 COMMENT '状态：1-在职，2-离职，0-禁用',
  `remark` text COMMENT '备注',
  `created_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updated_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `deleted_at` datetime DEFAULT NULL COMMENT '删除时间',
  PRIMARY KEY (`id`),
  KEY `idx_company_id` (`company_id`),
  KEY `idx_department_id` (`department_id`),
  KEY `idx_phone` (`phone`),
  KEY `idx_email` (`email`),
  KEY `idx_status` (`status`),
  KEY `idx_deleted_at` (`deleted_at`),
  UNIQUE KEY `uk_company_username` (`company_id`, `username`),
  UNIQUE KEY `uk_company_employee_no` (`company_id`, `employee_no`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='员工表';

-- 角色表
CREATE TABLE IF NOT EXISTS `ks_role` (
  `id` bigint(20) unsigned NOT NULL AUTO_INCREMENT COMMENT '角色ID',
  `company_id` bigint(20) unsigned NOT NULL COMMENT '公司ID',
  `name` varchar(50) NOT NULL COMMENT '角色名称',
  `description` text COMMENT '角色描述',
  `type` tinyint(1) DEFAULT 2 COMMENT '角色类型：1-系统角色，2-自定义角色',
  `permissions` text COMMENT '权限列表，JSON格式',
  `status` tinyint(1) DEFAULT 1 COMMENT '状态：1-正常，0-禁用',
  `created_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updated_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `deleted_at` datetime DEFAULT NULL COMMENT '删除时间',
  PRIMARY KEY (`id`),
  KEY `idx_company_id` (`company_id`),
  KEY `idx_type` (`type`),
  KEY `idx_status` (`status`),
  KEY `idx_deleted_at` (`deleted_at`),
  UNIQUE KEY `uk_company_name` (`company_id`, `name`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='角色表';

-- 员工角色关联表
CREATE TABLE IF NOT EXISTS `ks_employee_role` (
  `id` bigint(20) unsigned NOT NULL AUTO_INCREMENT COMMENT '关联ID',
  `employee_id` bigint(20) unsigned NOT NULL COMMENT '员工ID',
  `role_id` bigint(20) unsigned NOT NULL COMMENT '角色ID',
  `created_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  PRIMARY KEY (`id`),
  KEY `idx_employee_id` (`employee_id`),
  KEY `idx_role_id` (`role_id`),
  UNIQUE KEY `uk_employee_role` (`employee_id`, `role_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='员工角色关联表';

-- 添加外键约束
ALTER TABLE `ks_department`
  ADD CONSTRAINT `fk_department_company` FOREIGN KEY (`company_id`) REFERENCES `ks_company` (`id`) ON DELETE CASCADE,
  ADD CONSTRAINT `fk_department_parent` FOREIGN KEY (`parent_id`) REFERENCES `ks_department` (`id`) ON DELETE SET NULL,
  ADD CONSTRAINT `fk_department_manager` FOREIGN KEY (`manager_id`) REFERENCES `ks_employee` (`id`) ON DELETE SET NULL;

ALTER TABLE `ks_employee`
  ADD CONSTRAINT `fk_employee_company` FOREIGN KEY (`company_id`) REFERENCES `ks_company` (`id`) ON DELETE CASCADE,
  ADD CONSTRAINT `fk_employee_department` FOREIGN KEY (`department_id`) REFERENCES `ks_department` (`id`) ON DELETE RESTRICT;

ALTER TABLE `ks_role`
  ADD CONSTRAINT `fk_role_company` FOREIGN KEY (`company_id`) REFERENCES `ks_company` (`id`) ON DELETE CASCADE;

ALTER TABLE `ks_employee_role`
  ADD CONSTRAINT `fk_employee_role_employee` FOREIGN KEY (`employee_id`) REFERENCES `ks_employee` (`id`) ON DELETE CASCADE,
  ADD CONSTRAINT `fk_employee_role_role` FOREIGN KEY (`role_id`) REFERENCES `ks_role` (`id`) ON DELETE CASCADE;
