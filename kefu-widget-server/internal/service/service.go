package service

import (
	"kefu-server/internal/service/chat"
	"kefu-server/internal/service/company"
	"kefu-server/internal/service/department"
	"kefu-server/internal/service/employee"
	"kefu-server/internal/service/invite"
	"kefu-server/internal/service/role"
	"kefu-server/internal/service/session"
	"kefu-server/internal/service/settings"
	"kefu-server/internal/service/site"
	"kefu-server/internal/service/visitor"
)

// 服务实例
var (
	companyService    *company.Service
	siteService       *site.Service
	settingsService   *settings.Service
	chatService       *chat.Service
	sessionService    *session.Service
	departmentService *department.Service
	employeeService   *employee.Service
	roleService       *role.Service
	inviteService     *invite.Service
	visitorService    *visitor.Service
)

func init() {
	companyService = company.New()
	siteService = site.New()
	settingsService = settings.New()
	chatService = chat.New()
	sessionService = session.New()
	departmentService = department.New()
	employeeService = employee.New()
	roleService = role.New()
	inviteService = invite.New()
	visitorService = visitor.New()
}

// Company 公司服务
func Company() *company.Service {
	return companyService
}

// Site 站点服务
func Site() *site.Service {
	return siteService
}

// Settings 设置服务
func Settings() *settings.Service {
	return settingsService
}

// Chat 聊天服务
func Chat() *chat.Service {
	return chatService
}

// Session 会话服务
func Session() *session.Service {
	return sessionService
}

// Department 部门服务
func Department() *department.Service {
	return departmentService
}

// Employee 员工服务
func Employee() *employee.Service {
	return employeeService
}

// Role 角色服务
func Role() *role.Service {
	return roleService
}

// Invite 邀请服务
func Invite() *invite.Service {
	return inviteService
}

// Visitor 访客服务
func Visitor() *visitor.Service {
	return visitorService
}
