package department

import (
	"context"
	"errors"
	"kefu-server/internal/dao"
	"kefu-server/internal/model/entity"

	"github.com/gogf/gf/v2/frame/g"
	"github.com/gogf/gf/v2/os/gtime"
)

type Service struct{}

func New() *Service {
	return &Service{}
}

// CreateDepartment 创建部门
func (s *Service) CreateDepartment(ctx context.Context, req *CreateDepartmentReq) (*entity.Department, error) {
	// 检查部门编码是否已存在
	exists, err := dao.Department.Ctx(ctx).Where("code", req.Code).Where("company_id", req.CompanyId).Count()
	if err != nil {
		return nil, err
	}
	if exists > 0 {
		return nil, errors.New("部门编码已存在")
	}

	// 如果有上级部门，检查上级部门是否存在
	if req.ParentId > 0 {
		parent, err := dao.Department.Ctx(ctx).Where("id", req.ParentId).Where("company_id", req.CompanyId).One()
		if err != nil {
			return nil, err
		}
		if parent.IsEmpty() {
			return nil, errors.New("上级部门不存在")
		}
	}

	// 创建部门
	department := &entity.Department{
		CompanyId:   req.CompanyId,
		ParentId:    req.ParentId,
		Name:        req.Name,
		Code:        req.Code,
		Description: req.Description,
		ManagerId:   req.ManagerId,
		Level:       req.Level,
		Sort:        req.Sort,
		Status:      1, // 默认启用
	}

	result, err := dao.Department.Ctx(ctx).Data(department).Insert()
	if err != nil {
		return nil, err
	}

	id, err := result.LastInsertId()
	if err != nil {
		return nil, err
	}

	department.Id = uint(id)
	return department, nil
}

// UpdateDepartment 更新部门
func (s *Service) UpdateDepartment(ctx context.Context, req *UpdateDepartmentReq) error {
	// 检查部门是否存在
	exists, err := dao.Department.Ctx(ctx).Where("id", req.Id).Where("company_id", req.CompanyId).Count()
	if err != nil {
		return err
	}
	if exists == 0 {
		return errors.New("部门不存在")
	}

	// 检查部门编码是否已被其他部门使用
	if req.Code != "" {
		count, err := dao.Department.Ctx(ctx).Where("code", req.Code).Where("company_id", req.CompanyId).Where("id !=", req.Id).Count()
		if err != nil {
			return err
		}
		if count > 0 {
			return errors.New("部门编码已存在")
		}
	}

	// 更新部门信息
	updateData := g.Map{}
	if req.Name != "" {
		updateData["name"] = req.Name
	}
	if req.Code != "" {
		updateData["code"] = req.Code
	}
	if req.Description != "" {
		updateData["description"] = req.Description
	}
	if req.ManagerId > 0 {
		updateData["manager_id"] = req.ManagerId
	}
	if req.Level > 0 {
		updateData["level"] = req.Level
	}
	if req.Sort >= 0 {
		updateData["sort"] = req.Sort
	}
	if req.Status >= 0 {
		updateData["status"] = req.Status
	}

	_, err = dao.Department.Ctx(ctx).Where("id", req.Id).Where("company_id", req.CompanyId).Update(updateData)
	return err
}

// DeleteDepartment 删除部门
func (s *Service) DeleteDepartment(ctx context.Context, id uint, companyId uint) error {
	// 检查部门是否存在
	department, err := dao.Department.Ctx(ctx).Where("id", id).Where("company_id", companyId).One()
	if err != nil {
		return err
	}
	if department.IsEmpty() {
		return errors.New("部门不存在")
	}

	// 检查是否有子部门
	childCount, err := dao.Department.Ctx(ctx).Where("parent_id", id).Where("company_id", companyId).Count()
	if err != nil {
		return err
	}
	if childCount > 0 {
		return errors.New("该部门下还有子部门，无法删除")
	}

	// 检查是否有员工
	employeeCount, err := dao.Employee.Ctx(ctx).Where("department_id", id).Where("company_id", companyId).Count()
	if err != nil {
		return err
	}
	if employeeCount > 0 {
		return errors.New("该部门下还有员工，无法删除")
	}

	// 软删除部门
	_, err = dao.Department.Ctx(ctx).Where("id", id).Where("company_id", companyId).Update(g.Map{
		"status":     0,
		"deleted_at": gtime.Now(),
	})
	return err
}

// GetDepartmentById 根据ID获取部门信息
func (s *Service) GetDepartmentById(ctx context.Context, id uint, companyId uint) (*entity.Department, error) {
	result, err := dao.Department.Ctx(ctx).Where("id", id).Where("company_id", companyId).Where("status", 1).One()
	if err != nil {
		return nil, err
	}
	if result.IsEmpty() {
		return nil, nil
	}

	var department entity.Department
	if err := result.Struct(&department); err != nil {
		return nil, err
	}
	return &department, nil
}

// GetDepartmentList 获取部门列表
func (s *Service) GetDepartmentList(ctx context.Context, req *GetDepartmentListReq) ([]*entity.Department, int, error) {
	query := dao.Department.Ctx(ctx).Where("company_id", req.CompanyId).Where("status", 1)

	// 添加查询条件
	if req.ParentId >= 0 {
		query = query.Where("parent_id", req.ParentId)
	}
	if req.Name != "" {
		query = query.WhereLike("name", "%"+req.Name+"%")
	}
	if req.Code != "" {
		query = query.Where("code", req.Code)
	}

	// 获取总数
	total, err := query.Count()
	if err != nil {
		return nil, 0, err
	}

	// 分页查询
	if req.Page > 0 && req.Limit > 0 {
		offset := (req.Page - 1) * req.Limit
		query = query.Offset(offset).Limit(req.Limit)
	}

	// 排序
	query = query.Order("sort ASC, id ASC")

	results, err := query.All()
	if err != nil {
		return nil, 0, err
	}

	var departments []*entity.Department
	for _, result := range results {
		var department entity.Department
		if err := result.Struct(&department); err != nil {
			return nil, 0, err
		}
		departments = append(departments, &department)
	}

	return departments, total, nil
}

// GetDepartmentTree 获取部门树形结构
func (s *Service) GetDepartmentTree(ctx context.Context, companyId uint) ([]*DepartmentTreeNode, error) {
	// 获取所有部门
	results, err := dao.Department.Ctx(ctx).Where("company_id", companyId).Where("status", 1).Order("sort ASC, id ASC").All()
	if err != nil {
		return nil, err
	}

	var departments []*entity.Department
	for _, result := range results {
		var department entity.Department
		if err := result.Struct(&department); err != nil {
			return nil, err
		}
		departments = append(departments, &department)
	}

	// 构建树形结构
	return s.buildDepartmentTree(departments, 0), nil
}

// buildDepartmentTree 构建部门树形结构
func (s *Service) buildDepartmentTree(departments []*entity.Department, parentId uint) []*DepartmentTreeNode {
	var tree []*DepartmentTreeNode

	for _, dept := range departments {
		if dept.ParentId == parentId {
			node := &DepartmentTreeNode{
				Department: dept,
				Children:   s.buildDepartmentTree(departments, dept.Id),
			}
			tree = append(tree, node)
		}
	}

	return tree
}
