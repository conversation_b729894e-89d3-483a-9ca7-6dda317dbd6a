package invite

import (
	"github.com/gogf/gf/v2/os/gtime"
)

// CreateInviteReq 创建邀请访客会话记录请求
type CreateInviteReq struct {
	SiteId           uint   `json:"site_id"     v:"required#站点ID不能为空"`
	VisitorId        string `json:"visitor_id"  v:"required#访客ID不能为空"`
	AgentId          uint   `json:"agent_id"    v:"required#席位ID不能为空"`
	InviteType       int    `json:"invite_type" v:"required|in:1,2#邀请类型不能为空|邀请类型值无效"`
	InviteMessage    string `json:"invite_message"      v:"required#邀请消息不能为空"`
	VisitorIp        string `json:"visitor_ip"`
	VisitorUserAgent string `json:"visitor_user_agent"`
	VisitorPageUrl   string `json:"visitor_page_url"`
	VisitorPageTitle string `json:"visitor_page_title"`
	InviteTrigger    string `json:"invite_trigger"`
	Remark           string `json:"remark"`
}

// CreateInviteRes 创建邀请响应
type CreateInviteRes struct {
	Id         uint   `json:"id"`
	InviteTime string `json:"invite_time"`
}

// UpdateVisitorResponseReq 更新访客响应请求
type UpdateVisitorResponseReq struct {
	Id              uint   `json:"id"               v:"required#邀请记录ID不能为空"`
	VisitorResponse int    `json:"visitor_response" v:"required|in:1,2,3#访客响应不能为空|访客响应值无效"`
	SessionId       string `json:"session_id"`
}

// GetInviteListReq 获取邀请记录列表请求
type GetInviteListReq struct {
	SiteId          uint   `json:"site_id"`
	VisitorId       string `json:"visitor_id"`
	AgentId         uint   `json:"agent_id"`
	InviteType      int    `json:"invite_type"`
	VisitorResponse int    `json:"visitor_response"`
	StartTime       string `json:"start_time"`
	EndTime         string `json:"end_time"`
	Page            int    `json:"page"  v:"min:1#页码最小为1"`
	Limit           int    `json:"limit" v:"min:1|max:100#每页数量最小为1|每页数量最大为100"`
}

// InviteRecordWithDetails 邀请记录详情
type InviteRecordWithDetails struct {
	Id                  uint        `json:"id"`
	SiteId              uint        `json:"site_id"`
	SiteName            string      `json:"site_name"`
	VisitorId           string      `json:"visitor_id"`
	VisitorNickname     string      `json:"visitor_nickname"`
	AgentId             uint        `json:"agent_id"`
	AgentName           string      `json:"agent_name"`
	InviteType          int         `json:"invite_type"`
	InviteTypeText      string      `json:"invite_type_text"`
	InviteMessage       string      `json:"invite_message"`
	InviteTime          *gtime.Time `json:"invite_time"`
	VisitorResponse     int         `json:"visitor_response"`
	VisitorResponseText string      `json:"visitor_response_text"`
	ResponseTime        *gtime.Time `json:"response_time"`
	SessionId           string      `json:"session_id"`
	VisitorIp           string      `json:"visitor_ip"`
	VisitorUserAgent    string      `json:"visitor_user_agent"`
	VisitorPageUrl      string      `json:"visitor_page_url"`
	VisitorPageTitle    string      `json:"visitor_page_title"`
	InviteTrigger       string      `json:"invite_trigger"`
	Remark              string      `json:"remark"`
	CreatedAt           *gtime.Time `json:"created_at"`
	UpdatedAt           *gtime.Time `json:"updated_at"`
}

// GetInviteStatisticsReq 获取邀请统计请求
type GetInviteStatisticsReq struct {
	SiteId    uint   `json:"site_id"`
	AgentId   uint   `json:"agent_id"`
	StartTime string `json:"start_time"`
	EndTime   string `json:"end_time"`
}

// InviteStatistics 邀请统计
type InviteStatistics struct {
	TotalInvites    int     `json:"total_invites"`    // 总邀请数
	AcceptedInvites int     `json:"accepted_invites"` // 接受邀请数
	RejectedInvites int     `json:"rejected_invites"` // 拒绝邀请数
	IgnoredInvites  int     `json:"ignored_invites"`  // 忽略邀请数
	AcceptRate      float64 `json:"accept_rate"`      // 接受率
}
