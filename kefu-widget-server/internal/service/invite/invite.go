package invite

import (
	"context"
	"errors"

	"github.com/gogf/gf/v2/frame/g"
	"github.com/gogf/gf/v2/os/gtime"

	"kefu-server/internal/dao"
	"kefu-server/internal/model/entity"
)

// Service 邀请访客会话记录服务
type Service struct{}

// New 创建邀请记录服务实例
func New() *Service {
	return &Service{}
}

// CreateInvite 创建邀请访客会话记录
func (s *Service) CreateInvite(ctx context.Context, req *CreateInviteReq) (*CreateInviteRes, error) {
	// 1. 检查访客是否存在
	visitorExists, err := dao.Visitor.Ctx(ctx).Where("visitor_id", req.VisitorId).Count()
	if err != nil {
		return nil, err
	}
	if visitorExists == 0 {
		return nil, errors.New("访客不存在")
	}

	// 2. 检查席位是否存在
	agentExists, err := dao.Agent.Ctx(ctx).Where("id", req.AgentId).Count()
	if err != nil {
		return nil, err
	}
	if agentExists == 0 {
		return nil, errors.New("席位不存在")
	}

	// 3. 创建邀请记录
	inviteRecord := &entity.InviteRecord{
		SiteId:           req.SiteId,
		VisitorId:        req.VisitorId,
		AgentId:          req.AgentId,
		InviteType:       req.InviteType,
		InviteMessage:    req.InviteMessage,
		InviteTime:       gtime.Now(),
		VisitorIp:        req.VisitorIp,
		VisitorUserAgent: req.VisitorUserAgent,
		VisitorPageUrl:   req.VisitorPageUrl,
		VisitorPageTitle: req.VisitorPageTitle,
		InviteTrigger:    req.InviteTrigger,
		Remark:           req.Remark,
	}

	result, err := dao.InviteRecord.Ctx(ctx).Data(inviteRecord).Insert()
	if err != nil {
		return nil, err
	}

	id, err := result.LastInsertId()
	if err != nil {
		return nil, err
	}

	return &CreateInviteRes{
		Id:         uint(id),
		InviteTime: gtime.Now().Format("2006-01-02 15:04:05"),
	}, nil
}

// UpdateVisitorResponse 更新访客响应
func (s *Service) UpdateVisitorResponse(ctx context.Context, req *UpdateVisitorResponseReq) error {
	updateData := g.Map{
		"visitor_response": req.VisitorResponse,
		"response_time":    gtime.Now(),
	}

	if req.SessionId != "" {
		updateData["session_id"] = req.SessionId
	}

	_, err := dao.InviteRecord.Ctx(ctx).Where("id", req.Id).Update(updateData)
	return err
}

// GetInviteList 获取邀请记录列表
func (s *Service) GetInviteList(ctx context.Context, req *GetInviteListReq) ([]*InviteRecordWithDetails, int, error) {
	query := dao.InviteRecord.Ctx(ctx).
		LeftJoin("ks_site s", "ks_invite_record.site_id = s.id").
		LeftJoin("ks_visitor v", "ks_invite_record.visitor_id = v.visitor_id").
		LeftJoin("ks_agent a", "ks_invite_record.agent_id = a.id").
		Fields(`ks_invite_record.*,
			s.name as site_name,
			v.nickname as visitor_nickname,
			a.id as agent_name`)

	// 添加查询条件
	if req.SiteId > 0 {
		query = query.Where("ks_invite_record.site_id", req.SiteId)
	}
	if req.VisitorId != "" {
		query = query.Where("ks_invite_record.visitor_id", req.VisitorId)
	}
	if req.AgentId > 0 {
		query = query.Where("ks_invite_record.agent_id", req.AgentId)
	}
	if req.InviteType > 0 {
		query = query.Where("ks_invite_record.invite_type", req.InviteType)
	}
	if req.VisitorResponse > 0 {
		query = query.Where("ks_invite_record.visitor_response", req.VisitorResponse)
	}
	if req.StartTime != "" {
		query = query.Where("ks_invite_record.invite_time >=", req.StartTime)
	}
	if req.EndTime != "" {
		query = query.Where("ks_invite_record.invite_time <=", req.EndTime)
	}

	// 获取总数
	total, err := query.Count()
	if err != nil {
		return nil, 0, err
	}

	// 分页查询
	if req.Page > 0 && req.Limit > 0 {
		offset := (req.Page - 1) * req.Limit
		query = query.Offset(offset).Limit(req.Limit)
	}

	// 排序
	query = query.Order("ks_invite_record.invite_time DESC")

	results, err := query.All()
	if err != nil {
		return nil, 0, err
	}

	var invites []*InviteRecordWithDetails
	for _, result := range results {
		var invite InviteRecordWithDetails
		if err := result.Struct(&invite); err != nil {
			return nil, 0, err
		}
		invite.InviteTypeText = s.getInviteTypeText(invite.InviteType)
		invite.VisitorResponseText = s.getVisitorResponseText(invite.VisitorResponse)
		invites = append(invites, &invite)
	}

	return invites, total, nil
}

// GetInviteStatistics 获取邀请统计
func (s *Service) GetInviteStatistics(ctx context.Context, req *GetInviteStatisticsReq) (*InviteStatistics, error) {
	query := dao.InviteRecord.Ctx(ctx)

	// 添加查询条件
	if req.SiteId > 0 {
		query = query.Where("site_id", req.SiteId)
	}
	if req.AgentId > 0 {
		query = query.Where("agent_id", req.AgentId)
	}
	if req.StartTime != "" {
		query = query.Where("invite_time >=", req.StartTime)
	}
	if req.EndTime != "" {
		query = query.Where("invite_time <=", req.EndTime)
	}

	// 获取总邀请数
	totalInvites, err := query.Count()
	if err != nil {
		return nil, err
	}

	// 获取接受邀请数
	acceptedInvites, err := query.Where("visitor_response", entity.VisitorResponseAccept).Count()
	if err != nil {
		return nil, err
	}

	// 获取拒绝邀请数
	rejectedInvites, err := query.Where("visitor_response", entity.VisitorResponseReject).Count()
	if err != nil {
		return nil, err
	}

	// 获取忽略邀请数
	ignoredInvites, err := query.Where("visitor_response", entity.VisitorResponseIgnore).Count()
	if err != nil {
		return nil, err
	}

	// 计算接受率
	var acceptRate float64
	if totalInvites > 0 {
		acceptRate = float64(acceptedInvites) / float64(totalInvites) * 100
	}

	return &InviteStatistics{
		TotalInvites:    totalInvites,
		AcceptedInvites: acceptedInvites,
		RejectedInvites: rejectedInvites,
		IgnoredInvites:  ignoredInvites,
		AcceptRate:      acceptRate,
	}, nil
}

// DeleteInvite 删除邀请记录
func (s *Service) DeleteInvite(ctx context.Context, id uint) error {
	_, err := dao.InviteRecord.Ctx(ctx).Where("id", id).Delete()
	return err
}

// getInviteTypeText 获取邀请类型文本
func (s *Service) getInviteTypeText(inviteType int) string {
	switch inviteType {
	case entity.InviteTypeManual:
		return "主动邀请"
	case entity.InviteTypeAuto:
		return "自动邀请"
	default:
		return "未知类型"
	}
}

// getVisitorResponseText 获取访客响应文本
func (s *Service) getVisitorResponseText(response int) string {
	switch response {
	case entity.VisitorResponseAccept:
		return "接受"
	case entity.VisitorResponseReject:
		return "拒绝"
	case entity.VisitorResponseIgnore:
		return "忽略"
	default:
		return "未响应"
	}
}
