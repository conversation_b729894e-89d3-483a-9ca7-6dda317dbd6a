package visitor

// SignInReq 访客签到请求
type SignInReq struct {
	SiteKey   string `json:"site_key" v:"required#站点密钥不能为空"`
	VisitorId string `json:"visitor_id" v:"required#访客ID不能为空"`
	UserAgent string `json:"user_agent"`
	PageUrl   string `json:"page_url"`
	PageTitle string `json:"page_title"`
	Referrer  string `json:"referrer"`
}

// SignInRes 访客签到响应
type SignInRes struct {
	VisitorId    string `json:"visitor_id"`
	IsNewVisitor bool   `json:"is_new_visitor"`
	Id           uint64 `json:"id"`
}
