package visitor

import (
	"context"
	"errors"
	"net/url"
	"regexp"
	"strings"

	"kefu-server/internal/dao"
	"kefu-server/internal/model/entity"

	"github.com/gogf/gf/v2/frame/g"
	"github.com/gogf/gf/v2/os/gtime"
)

type Service struct{}

func New() *Service {
	return &Service{}
}

// SignIn 访客签到
func (s *Service) SignIn(ctx context.Context, req *SignInReq) (*SignInRes, error) {
	// 1. 根据site_key获取站点信息
	site, err := dao.Site.Ctx(ctx).Where("site_key", req.SiteKey).Where("status", 1).One()
	if err != nil {
		return nil, err
	}
	if site.IsEmpty() {
		return nil, errors.New("站点不存在或已禁用")
	}

	siteId := site["id"].Uint64()

	// 2. 解析请求信息
	hostName := s.extractHostName(req.PageUrl)
	browserName, osName := s.parseUserAgent(req.UserAgent)

	// 3. 检查访客是否存在
	visitor, err := dao.Visitor.Ctx(ctx).Where("visitor_id", req.VisitorId).One()
	if err != nil {
		return nil, err
	}

	now := gtime.Now()

	if visitor.IsEmpty() {
		// 4. 创建新访客记录
		newVisitor := &entity.Visitor{
			SiteId:       siteId,
			VisitorId:    req.VisitorId,
			HostName:     hostName,
			BrowserName:  browserName,
			OsName:       osName,
			UserAgent:    req.UserAgent,
			PageUrl:      req.PageUrl,
			PageTitle:    req.PageTitle,
			Referrer:     req.Referrer,
			SignInTime:   now,
			FirstVisit:   now,
			LastVisit:    now,
			InviteTimes:  0,
			InviteStatus: 0,
			RefuseTimes:  0,
			Status:       1,
		}

		result, err := dao.Visitor.Ctx(ctx).Data(newVisitor).Insert()
		if err != nil {
			return nil, err
		}

		id, err := result.LastInsertId()
		if err != nil {
			return nil, err
		}

		return &SignInRes{
			VisitorId:    req.VisitorId,
			IsNewVisitor: true,
			Id:           uint64(id),
		}, nil
	} else {
		// 5. 更新现有访客记录
		updateData := g.Map{
			"host_name":    hostName,
			"browser_name": browserName,
			"os_name":      osName,
			"user_agent":   req.UserAgent,
			"page_url":     req.PageUrl,
			"page_title":   req.PageTitle,
			"referrer":     req.Referrer,
			"sign_in_time": now,
			"last_visit":   now,
			"updated_at":   now,
		}

		_, err := dao.Visitor.Ctx(ctx).Where("visitor_id", req.VisitorId).Update(updateData)
		if err != nil {
			return nil, err
		}

		return &SignInRes{
			VisitorId:    req.VisitorId,
			IsNewVisitor: false,
			Id:           visitor["id"].Uint64(),
		}, nil
	}
}

// extractHostName 从URL中提取主机名
func (s *Service) extractHostName(pageUrl string) string {
	if pageUrl == "" {
		return ""
	}

	parsedUrl, err := url.Parse(pageUrl)
	if err != nil {
		return ""
	}

	return parsedUrl.Host
}

// parseUserAgent 解析UserAgent获取浏览器名称和操作系统名称
func (s *Service) parseUserAgent(userAgent string) (browserName, osName string) {
	if userAgent == "" {
		return "Unknown", "Unknown"
	}

	userAgent = strings.ToLower(userAgent)

	// 解析浏览器名称
	browserName = s.parseBrowserName(userAgent)

	// 解析操作系统名称
	osName = s.parseOSName(userAgent)

	return browserName, osName
}

// parseBrowserName 解析浏览器名称
func (s *Service) parseBrowserName(userAgent string) string {
	// 浏览器检测规则（按优先级排序）
	browsers := []struct {
		name    string
		pattern string
	}{
		{"Edge", `edg/`},
		{"Chrome", `chrome/`},
		{"Firefox", `firefox/`},
		{"Safari", `safari/`},
		{"Opera", `opera/|opr/`},
		{"Internet Explorer", `msie|trident/`},
	}

	for _, browser := range browsers {
		matched, _ := regexp.MatchString(browser.pattern, userAgent)
		if matched {
			return browser.name
		}
	}

	return "Unknown"
}

// parseOSName 解析操作系统名称
func (s *Service) parseOSName(userAgent string) string {
	// 操作系统检测规则
	systems := []struct {
		name    string
		pattern string
	}{
		{"Windows 11", `windows nt 10\.0.*\) edg/`},
		{"Windows 10", `windows nt 10\.0`},
		{"Windows 8.1", `windows nt 6\.3`},
		{"Windows 8", `windows nt 6\.2`},
		{"Windows 7", `windows nt 6\.1`},
		{"Windows Vista", `windows nt 6\.0`},
		{"Windows XP", `windows nt 5\.1`},
		{"Windows", `windows`},
		{"macOS", `mac os x|macos`},
		{"iOS", `iphone|ipad|ipod`},
		{"Android", `android`},
		{"Linux", `linux`},
		{"Unix", `unix`},
	}

	for _, system := range systems {
		matched, _ := regexp.MatchString(system.pattern, userAgent)
		if matched {
			return system.name
		}
	}

	return "Unknown"
}
