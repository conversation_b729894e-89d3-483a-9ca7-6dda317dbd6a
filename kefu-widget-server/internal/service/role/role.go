package role

import (
	"context"
	"errors"
	"kefu-server/internal/dao"
	"kefu-server/internal/model/entity"

	"github.com/gogf/gf/v2/frame/g"
	"github.com/gogf/gf/v2/os/gtime"
)

type Service struct{}

func New() *Service {
	return &Service{}
}

// CreateRole 创建角色
func (s *Service) CreateRole(ctx context.Context, req *CreateRoleReq) (*entity.Role, error) {
	// 检查角色编码是否已存在
	exists, err := dao.Role.Ctx(ctx).Where("code", req.Code).Where("company_id", req.CompanyId).Count()
	if err != nil {
		return nil, err
	}
	if exists > 0 {
		return nil, errors.New("角色编码已存在")
	}

	// 检查角色名称是否已存在
	exists, err = dao.Role.Ctx(ctx).Where("name", req.Name).Where("company_id", req.CompanyId).Count()
	if err != nil {
		return nil, err
	}
	if exists > 0 {
		return nil, errors.New("角色名称已存在")
	}

	// 创建角色
	role := &entity.Role{
		CompanyId:   req.CompanyId,
		Name:        req.Name,
		Code:        req.Code,
		Description: req.Description,
		Type:        req.Type,
		Level:       req.Level,
		Permissions: req.Permissions,
		Status:      1, // 默认启用
	}

	result, err := dao.Role.Ctx(ctx).Data(role).Insert()
	if err != nil {
		return nil, err
	}

	id, err := result.LastInsertId()
	if err != nil {
		return nil, err
	}

	role.Id = uint(id)
	return role, nil
}

// UpdateRole 更新角色
func (s *Service) UpdateRole(ctx context.Context, req *UpdateRoleReq) error {
	// 检查角色是否存在
	exists, err := dao.Role.Ctx(ctx).Where("id", req.Id).Where("company_id", req.CompanyId).Count()
	if err != nil {
		return err
	}
	if exists == 0 {
		return errors.New("角色不存在")
	}

	// 检查角色编码是否已被其他角色使用
	if req.Code != "" {
		count, err := dao.Role.Ctx(ctx).Where("code", req.Code).Where("company_id", req.CompanyId).Where("id !=", req.Id).Count()
		if err != nil {
			return err
		}
		if count > 0 {
			return errors.New("角色编码已存在")
		}
	}

	// 检查角色名称是否已被其他角色使用
	if req.Name != "" {
		count, err := dao.Role.Ctx(ctx).Where("name", req.Name).Where("company_id", req.CompanyId).Where("id !=", req.Id).Count()
		if err != nil {
			return err
		}
		if count > 0 {
			return errors.New("角色名称已存在")
		}
	}

	// 更新角色信息
	updateData := g.Map{}
	if req.Name != "" {
		updateData["name"] = req.Name
	}
	if req.Code != "" {
		updateData["code"] = req.Code
	}
	if req.Description != "" {
		updateData["description"] = req.Description
	}
	if req.Type > 0 {
		updateData["type"] = req.Type
	}
	if req.Level > 0 {
		updateData["level"] = req.Level
	}
	if req.Permissions != "" {
		updateData["permissions"] = req.Permissions
	}
	if req.Status >= 0 {
		updateData["status"] = req.Status
	}

	_, err = dao.Role.Ctx(ctx).Where("id", req.Id).Where("company_id", req.CompanyId).Update(updateData)
	return err
}

// DeleteRole 删除角色
func (s *Service) DeleteRole(ctx context.Context, id uint, companyId uint) error {
	// 检查角色是否存在
	role, err := dao.Role.Ctx(ctx).Where("id", id).Where("company_id", companyId).One()
	if err != nil {
		return err
	}
	if role.IsEmpty() {
		return errors.New("角色不存在")
	}

	// 检查是否有员工使用该角色
	employeeCount, err := dao.EmployeeRole.Ctx(ctx).Where("role_id", id).Count()
	if err != nil {
		return err
	}
	if employeeCount > 0 {
		return errors.New("该角色下还有员工，无法删除")
	}

	// 软删除角色
	_, err = dao.Role.Ctx(ctx).Where("id", id).Where("company_id", companyId).Update(g.Map{
		"status":     0,
		"deleted_at": gtime.Now(),
	})
	return err
}

// GetRoleById 根据ID获取角色信息
func (s *Service) GetRoleById(ctx context.Context, id uint, companyId uint) (*entity.Role, error) {
	result, err := dao.Role.Ctx(ctx).Where("id", id).Where("company_id", companyId).Where("status", 1).One()
	if err != nil {
		return nil, err
	}
	if result.IsEmpty() {
		return nil, nil
	}

	var role entity.Role
	if err := result.Struct(&role); err != nil {
		return nil, err
	}
	return &role, nil
}

// GetRoleList 获取角色列表
func (s *Service) GetRoleList(ctx context.Context, req *GetRoleListReq) ([]*entity.Role, int, error) {
	query := dao.Role.Ctx(ctx).Where("company_id", req.CompanyId).Where("status", 1)

	// 添加查询条件
	if req.Name != "" {
		query = query.WhereLike("name", "%"+req.Name+"%")
	}
	if req.Code != "" {
		query = query.Where("code", req.Code)
	}
	if req.Type > 0 {
		query = query.Where("type", req.Type)
	}

	// 获取总数
	total, err := query.Count()
	if err != nil {
		return nil, 0, err
	}

	// 分页查询
	if req.Page > 0 && req.Limit > 0 {
		offset := (req.Page - 1) * req.Limit
		query = query.Offset(offset).Limit(req.Limit)
	}

	// 排序
	query = query.Order("level ASC, id ASC")

	results, err := query.All()
	if err != nil {
		return nil, 0, err
	}

	var roles []*entity.Role
	for _, result := range results {
		var role entity.Role
		if err := result.Struct(&role); err != nil {
			return nil, 0, err
		}
		roles = append(roles, &role)
	}

	return roles, total, nil
}

// GetRolesByEmployeeId 根据员工ID获取角色列表
func (s *Service) GetRolesByEmployeeId(ctx context.Context, employeeId uint) ([]*entity.Role, error) {
	results, err := dao.Role.Ctx(ctx).
		LeftJoin("ks_employee_role er", "ks_role.id = er.role_id").
		Where("er.employee_id", employeeId).
		Where("ks_role.status", 1).
		All()
	if err != nil {
		return nil, err
	}

	var roles []*entity.Role
	for _, result := range results {
		var role entity.Role
		if err := result.Struct(&role); err != nil {
			return nil, err
		}
		roles = append(roles, &role)
	}

	return roles, nil
}

// AssignRoleToEmployee 为员工分配角色
func (s *Service) AssignRoleToEmployee(ctx context.Context, employeeId uint, roleIds []uint) error {
	// 先删除原有角色
	_, err := dao.EmployeeRole.Ctx(ctx).Where("employee_id", employeeId).Delete()
	if err != nil {
		return err
	}

	// 分配新角色
	for _, roleId := range roleIds {
		employeeRole := &entity.EmployeeRole{
			EmployeeId: employeeId,
			RoleId:     roleId,
		}
		_, err := dao.EmployeeRole.Ctx(ctx).Data(employeeRole).Insert()
		if err != nil {
			return err
		}
	}

	return nil
}

// RemoveRoleFromEmployee 移除员工角色
func (s *Service) RemoveRoleFromEmployee(ctx context.Context, employeeId uint, roleId uint) error {
	_, err := dao.EmployeeRole.Ctx(ctx).Where("employee_id", employeeId).Where("role_id", roleId).Delete()
	return err
}
