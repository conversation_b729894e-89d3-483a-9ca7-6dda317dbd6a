package session

import (
	"context"
	"encoding/json"
	"fmt"
	"kefu-server/internal/dao"
	"kefu-server/internal/model/entity"
	"time"

	"github.com/gogf/gf/v2/crypto/gmd5"
	"github.com/gogf/gf/v2/frame/g"
	"github.com/gogf/gf/v2/os/gtime"
	"github.com/gogf/gf/v2/util/grand"
)

type Service struct{}

func New() *Service {
	return &Service{}
}

// CreateSession 创建访客会话
func (s *Service) CreateSession(ctx context.Context, req *CreateSessionReq) (*entity.VisitorSession, error) {
	// 生成会话ID
	sessionId := s.generateSessionId()

	// 获取访客信息
	visitorInfo := map[string]interface{}{
		"nickname":   req.Nickname,
		"avatar":     req.Avatar,
		"email":      req.Email,
		"phone":      req.Phone,
		"ip_address": req.<PERSON><PERSON><PERSON><PERSON><PERSON>,
		"user_agent": req.UserAgent,
		"referrer":   req.Referrer,
		"page_url":   req.PageUrl,
		"page_title": req.PageTitle,
	}

	// 将访客信息转换为JSON字符串
	visitorInfoJson, _ := json.Marshal(visitorInfo)

	// 创建会话记录
	session := &entity.VisitorSession{
		SessionId:        sessionId,
		SiteId:           req.SiteId,
		CompanyId:        req.CompanyId,
		VisitorId:        req.VisitorId,
		Status:           0, // 等待中
		Priority:         req.Priority,
		Source:           req.Source,
		VisitorInfo:      string(visitorInfoJson),
		StartTime:        gtime.Now(),
		LastActivityTime: gtime.Now(),
	}

	// 保存到数据库
	result, err := dao.VisitorSession.Ctx(ctx).Data(session).Insert()
	if err != nil {
		return nil, err
	}

	// 获取插入的ID
	id, err := result.LastInsertId()
	if err != nil {
		return nil, err
	}
	session.Id = uint64(id)

	// 如果需要排队，加入排队
	if req.NeedQueue {
		queueReq := &JoinQueueReq{
			SessionId:    sessionId,
			SiteId:       req.SiteId,
			CompanyId:    req.CompanyId,
			VisitorId:    req.VisitorId,
			SkillGroupId: req.SkillGroupId,
			Priority:     req.Priority,
		}
		_, err = s.JoinQueue(ctx, queueReq)
		if err != nil {
			g.Log().Error(ctx, "加入排队失败:", err)
		}
	}

	return session, nil
}

// JoinQueue 加入排队
func (s *Service) JoinQueue(ctx context.Context, req *JoinQueueReq) (*entity.SessionQueue, error) {
	// 获取当前排队位置
	queuePosition, err := s.getNextQueuePosition(ctx, req.SiteId, req.SkillGroupId)
	if err != nil {
		return nil, err
	}

	// 估算等待时间
	estimatedWait := s.calculateEstimatedWait(ctx, req.SiteId, req.SkillGroupId, queuePosition)

	// 创建排队记录
	queue := &entity.SessionQueue{
		SessionId:     req.SessionId,
		SiteId:        req.SiteId,
		CompanyId:     req.CompanyId,
		VisitorId:     req.VisitorId,
		SkillGroupId:  req.SkillGroupId,
		Priority:      req.Priority,
		QueuePosition: queuePosition,
		EstimatedWait: estimatedWait,
		Status:        0, // 排队中
		JoinTime:      gtime.Now(),
	}

	// 保存到数据库
	result, err := dao.SessionQueue.Ctx(ctx).Data(queue).Insert()
	if err != nil {
		return nil, err
	}

	// 获取插入的ID
	id, err := result.LastInsertId()
	if err != nil {
		return nil, err
	}
	queue.Id = uint64(id)

	return queue, nil
}

// AssignSession 分配会话给客服
func (s *Service) AssignSession(ctx context.Context, req *AssignSessionReq) error {
	// 更新会话状态
	_, err := dao.VisitorSession.Ctx(ctx).
		Where("session_id", req.SessionId).
		Data(g.Map{
			"customer_service_id": req.CustomerServiceId,
			"skill_group_id":      req.SkillGroupId,
			"status":              1, // 进行中
			"start_time":          gtime.Now(),
			"updated_at":          gtime.Now(),
		}).
		Update()

	if err != nil {
		return err
	}

	// 更新排队状态
	_, err = dao.SessionQueue.Ctx(ctx).
		Where("session_id", req.SessionId).
		Data(g.Map{
			"status":      1, // 已分配
			"assign_time": gtime.Now(),
			"updated_at":  gtime.Now(),
		}).
		Update()

	return err
}

// EndSession 结束会话
func (s *Service) EndSession(ctx context.Context, req *EndSessionReq) error {
	// 获取会话信息
	session, err := dao.VisitorSession.Ctx(ctx).Where("session_id", req.SessionId).One()
	if err != nil {
		return err
	}
	if session.IsEmpty() {
		return fmt.Errorf("会话不存在")
	}

	// 计算会话持续时间
	startTime := session["start_time"].Time()
	duration := int(time.Now().Sub(startTime).Seconds())

	// 更新会话状态
	_, err = dao.VisitorSession.Ctx(ctx).
		Where("session_id", req.SessionId).
		Data(g.Map{
			"status":     2, // 已结束
			"end_time":   gtime.Now(),
			"duration":   duration,
			"end_reason": req.EndReason,
			"updated_at": gtime.Now(),
		}).
		Update()

	return err
}

// TransferSession 转接会话
func (s *Service) TransferSession(ctx context.Context, req *TransferSessionReq) error {
	// 创建转接记录
	transfer := &entity.SessionTransfer{
		SessionId:             req.SessionId,
		FromCustomerServiceId: req.FromCustomerServiceId,
		ToCustomerServiceId:   req.ToCustomerServiceId,
		FromSkillGroupId:      req.FromSkillGroupId,
		ToSkillGroupId:        req.ToSkillGroupId,
		TransferType:          req.TransferType,
		TransferReason:        req.TransferReason,
		Status:                0, // 待接受
		TransferTime:          gtime.Now(),
	}

	// 保存转接记录
	_, err := dao.SessionTransfer.Ctx(ctx).Data(transfer).Insert()
	if err != nil {
		return err
	}

	// 更新会话状态
	_, err = dao.VisitorSession.Ctx(ctx).
		Where("session_id", req.SessionId).
		Data(g.Map{
			"status":          3, // 已转接
			"transfer_reason": req.TransferReason,
			"updated_at":      gtime.Now(),
		}).
		Update()

	return err
}

// GetSessionList 获取会话列表
func (s *Service) GetSessionList(ctx context.Context, req *GetSessionListReq) ([]*entity.VisitorSession, int, error) {
	query := dao.VisitorSession.Ctx(ctx)

	// 添加查询条件
	if req.SiteId > 0 {
		query = query.Where("site_id", req.SiteId)
	}
	if req.CompanyId > 0 {
		query = query.Where("company_id", req.CompanyId)
	}
	if req.CustomerServiceId > 0 {
		query = query.Where("customer_service_id", req.CustomerServiceId)
	}
	if req.Status >= 0 {
		query = query.Where("status", req.Status)
	}
	if req.StartTime != "" {
		query = query.Where("start_time >=", req.StartTime)
	}
	if req.EndTime != "" {
		query = query.Where("start_time <=", req.EndTime)
	}

	// 获取总数
	total, err := query.Count()
	if err != nil {
		return nil, 0, err
	}

	// 分页查询
	offset := (req.Page - 1) * req.Limit
	result, err := query.
		Order("created_at DESC").
		Limit(offset, req.Limit).
		All()
	if err != nil {
		return nil, 0, err
	}

	// 转换为实体
	var sessions []*entity.VisitorSession
	err = result.Structs(&sessions)
	if err != nil {
		return nil, 0, err
	}

	return sessions, total, nil
}

// 私有方法

// generateSessionId 生成会话ID
func (s *Service) generateSessionId() string {
	randomStr := grand.S(16)
	timestamp := gtime.Now().String()
	return "session_" + gmd5.MustEncrypt(randomStr + timestamp)[:16]
}

// getNextQueuePosition 获取下一个排队位置
func (s *Service) getNextQueuePosition(ctx context.Context, siteId, skillGroupId uint64) (int, error) {
	query := dao.SessionQueue.Ctx(ctx).Where("site_id", siteId).Where("status", 0)
	if skillGroupId > 0 {
		query = query.Where("skill_group_id", skillGroupId)
	}

	count, err := query.Count()
	if err != nil {
		return 0, err
	}

	return count + 1, nil
}

// calculateEstimatedWait 计算预估等待时间
func (s *Service) calculateEstimatedWait(ctx context.Context, siteId, skillGroupId uint64, queuePosition int) int {
	// 简单的估算逻辑：每个位置预估等待3分钟
	// 实际项目中可以根据历史数据和当前客服状态进行更精确的计算
	return queuePosition * 180 // 3分钟 = 180秒
}
