package employee

import (
	"context"
	"errors"
	"kefu-server/internal/dao"
	"kefu-server/internal/model/entity"

	"github.com/gogf/gf/v2/crypto/gmd5"
	"github.com/gogf/gf/v2/database/gdb"
	"github.com/gogf/gf/v2/frame/g"
	"github.com/gogf/gf/v2/os/gtime"
	"github.com/gogf/gf/v2/util/grand"
)

type Service struct{}

func New() *Service {
	return &Service{}
}

// CreateEmployee 创建员工
func (s *Service) CreateEmployee(ctx context.Context, req *CreateEmployeeReq) (*entity.Employee, error) {
	// 检查用户名是否已存在
	exists, err := dao.Employee.Ctx(ctx).Where("username", req.Username).Where("company_id", req.CompanyId).Count()
	if err != nil {
		return nil, err
	}
	if exists > 0 {
		return nil, errors.New("用户名已存在")
	}

	// 检查工号是否已存在
	if req.EmployeeNo != "" {
		exists, err := dao.Employee.Ctx(ctx).Where("employee_no", req.EmployeeNo).Where("company_id", req.CompanyId).Count()
		if err != nil {
			return nil, err
		}
		if exists > 0 {
			return nil, errors.New("工号已存在")
		}
	}

	// 检查部门是否存在
	if req.DepartmentId > 0 {
		dept, err := dao.Department.Ctx(ctx).Where("id", req.DepartmentId).Where("company_id", req.CompanyId).One()
		if err != nil {
			return nil, err
		}
		if dept.IsEmpty() {
			return nil, errors.New("部门不存在")
		}
	}

	// 加密密码
	hashedPassword, err := s.hashPassword(req.Password)
	if err != nil {
		return nil, err
	}

	// 创建员工
	employee := &entity.Employee{
		CompanyId:    req.CompanyId,
		DepartmentId: req.DepartmentId,
		EmployeeNo:   req.EmployeeNo,
		Username:     req.Username,
		Password:     hashedPassword,
		RealName:     req.RealName,
		Nickname:     req.Nickname,
		Avatar:       req.Avatar,
		Gender:       req.Gender,
		Birthday:     req.Birthday,
		Phone:        req.Phone,
		Email:        req.Email,
		EntryDate:    req.EntryDate,
		Status:       1, // 默认在职
		Remark:       req.Remark,
	}

	result, err := dao.Employee.Ctx(ctx).Data(employee).Insert()
	if err != nil {
		return nil, err
	}

	id, err := result.LastInsertId()
	if err != nil {
		return nil, err
	}

	employee.Id = uint(id)

	// 分配角色
	if len(req.RoleIds) > 0 {
		err = s.assignRoles(ctx, employee.Id, req.RoleIds)
		if err != nil {
			return nil, err
		}
	}

	return employee, nil
}

// UpdateEmployee 更新员工
func (s *Service) UpdateEmployee(ctx context.Context, req *UpdateEmployeeReq) error {
	// 检查员工是否存在
	exists, err := dao.Employee.Ctx(ctx).Where("id", req.Id).Where("company_id", req.CompanyId).Count()
	if err != nil {
		return err
	}
	if exists == 0 {
		return errors.New("员工不存在")
	}

	// 检查用户名是否已被其他员工使用
	if req.Username != "" {
		count, err := dao.Employee.Ctx(ctx).Where("username", req.Username).Where("company_id", req.CompanyId).Where("id !=", req.Id).Count()
		if err != nil {
			return err
		}
		if count > 0 {
			return errors.New("用户名已存在")
		}
	}

	// 检查工号是否已被其他员工使用
	if req.EmployeeNo != "" {
		count, err := dao.Employee.Ctx(ctx).Where("employee_no", req.EmployeeNo).Where("company_id", req.CompanyId).Where("id !=", req.Id).Count()
		if err != nil {
			return err
		}
		if count > 0 {
			return errors.New("工号已存在")
		}
	}

	// 更新员工信息
	updateData := g.Map{}
	if req.DepartmentId > 0 {
		updateData["department_id"] = req.DepartmentId
	}
	if req.EmployeeNo != "" {
		updateData["employee_no"] = req.EmployeeNo
	}
	if req.Username != "" {
		updateData["username"] = req.Username
	}
	if req.Password != "" {
		hashedPassword, err := s.hashPassword(req.Password)
		if err != nil {
			return err
		}
		updateData["password"] = hashedPassword
	}
	if req.RealName != "" {
		updateData["real_name"] = req.RealName
	}
	if req.Nickname != "" {
		updateData["nickname"] = req.Nickname
	}
	if req.Avatar != "" {
		updateData["avatar"] = req.Avatar
	}
	if req.Gender >= 0 {
		updateData["gender"] = req.Gender
	}
	if req.Birthday != nil {
		updateData["birthday"] = req.Birthday
	}
	if req.Phone != "" {
		updateData["phone"] = req.Phone
	}
	if req.Email != "" {
		updateData["email"] = req.Email
	}
	if req.IdCard != "" {
		updateData["id_card"] = req.IdCard
	}
	if req.Address != "" {
		updateData["address"] = req.Address
	}
	if req.EntryDate != nil {
		updateData["entry_date"] = req.EntryDate
	}
	if req.LeaveDate != nil {
		updateData["leave_date"] = req.LeaveDate
	}
	if req.Position != "" {
		updateData["position"] = req.Position
	}
	if req.Level != "" {
		updateData["level"] = req.Level
	}
	if req.Salary > 0 {
		updateData["salary"] = req.Salary
	}
	if req.Status >= 0 {
		updateData["status"] = req.Status
	}
	if req.Remark != "" {
		updateData["remark"] = req.Remark
	}

	_, err = dao.Employee.Ctx(ctx).Where("id", req.Id).Where("company_id", req.CompanyId).Update(updateData)
	if err != nil {
		return err
	}

	// 更新角色
	if len(req.RoleIds) > 0 {
		// 先删除原有角色
		_, err = dao.EmployeeRole.Ctx(ctx).Where("employee_id", req.Id).Delete()
		if err != nil {
			return err
		}
		// 重新分配角色
		err = s.assignRoles(ctx, req.Id, req.RoleIds)
		if err != nil {
			return err
		}
	}

	return nil
}

// DeleteEmployee 删除员工
func (s *Service) DeleteEmployee(ctx context.Context, id uint, companyId uint) error {
	// 检查员工是否存在
	employee, err := dao.Employee.Ctx(ctx).Where("id", id).Where("company_id", companyId).One()
	if err != nil {
		return err
	}
	if employee.IsEmpty() {
		return errors.New("员工不存在")
	}

	// 软删除员工
	_, err = dao.Employee.Ctx(ctx).Where("id", id).Where("company_id", companyId).Update(g.Map{
		"status":     0,
		"deleted_at": gtime.Now(),
	})
	if err != nil {
		return err
	}

	// 删除员工角色关联
	_, err = dao.EmployeeRole.Ctx(ctx).Where("employee_id", id).Delete()
	return err
}

// GetEmployeeById 根据ID获取员工信息
func (s *Service) GetEmployeeById(ctx context.Context, id uint, companyId uint) (*EmployeeWithRoles, error) {
	result, err := dao.Employee.Ctx(ctx).Where("id", id).Where("company_id", companyId).Where("status", 1).One()
	if err != nil {
		return nil, err
	}
	if result.IsEmpty() {
		return nil, nil
	}

	var employee entity.Employee
	if err := result.Struct(&employee); err != nil {
		return nil, err
	}

	// 获取员工角色
	roles, err := s.getEmployeeRoles(ctx, id)
	if err != nil {
		return nil, err
	}

	return &EmployeeWithRoles{
		Employee: &employee,
		Roles:    roles,
	}, nil
}

// GetEmployeeList 获取员工列表
func (s *Service) GetEmployeeList(ctx context.Context, req *GetEmployeeListReq) ([]*EmployeeWithRoles, int, error) {
	query := dao.Employee.Ctx(ctx).Where("company_id", req.CompanyId).Where("status", 1)

	// 添加查询条件
	if req.DepartmentId > 0 {
		query = query.Where("department_id", req.DepartmentId)
	}
	if req.RealName != "" {
		query = query.WhereLike("real_name", "%"+req.RealName+"%")
	}
	if req.Username != "" {
		query = query.WhereLike("username", "%"+req.Username+"%")
	}
	if req.Phone != "" {
		query = query.Where("phone", req.Phone)
	}
	if req.Email != "" {
		query = query.Where("email", req.Email)
	}
	if req.Position != "" {
		query = query.WhereLike("position", "%"+req.Position+"%")
	}

	// 获取总数
	total, err := query.Count()
	if err != nil {
		return nil, 0, err
	}

	// 分页查询
	if req.Page > 0 && req.Limit > 0 {
		offset := (req.Page - 1) * req.Limit
		query = query.Offset(offset).Limit(req.Limit)
	}

	// 排序
	query = query.Order("id DESC")

	results, err := query.All()
	if err != nil {
		return nil, 0, err
	}

	var employees []*EmployeeWithRoles
	for _, result := range results {
		var employee entity.Employee
		if err := result.Struct(&employee); err != nil {
			return nil, 0, err
		}

		// 获取员工角色
		roles, err := s.getEmployeeRoles(ctx, employee.Id)
		if err != nil {
			return nil, 0, err
		}

		employees = append(employees, &EmployeeWithRoles{
			Employee: &employee,
			Roles:    roles,
		})
	}

	return employees, total, nil
}

// assignRoles 分配角色
func (s *Service) assignRoles(ctx context.Context, employeeId uint, roleIds []uint) error {
	for _, roleId := range roleIds {
		employeeRole := &entity.EmployeeRole{
			EmployeeId: employeeId,
			RoleId:     roleId,
		}
		_, err := dao.EmployeeRole.Ctx(ctx).Data(employeeRole).Insert()
		if err != nil {
			return err
		}
	}
	return nil
}

// getEmployeeRoles 获取员工角色
func (s *Service) getEmployeeRoles(ctx context.Context, employeeId uint) ([]*entity.Role, error) {
	results, err := dao.Role.Ctx(ctx).
		LeftJoin("ks_employee_role er", "ks_role.id = er.role_id").
		Where("er.employee_id", employeeId).
		Where("ks_role.status", 1).
		All()
	if err != nil {
		return nil, err
	}

	var roles []*entity.Role
	for _, result := range results {
		var role entity.Role
		if err := result.Struct(&role); err != nil {
			return nil, err
		}
		roles = append(roles, &role)
	}

	return roles, nil
}

// hashPassword 加密密码
func (s *Service) hashPassword(password string) (string, error) {
	salt := grand.S(8)
	return gmd5.MustEncrypt(password + salt), nil
}

// RegisterUser 用户注册
func (s *Service) RegisterUser(ctx context.Context, req *RegisterUserReq) (*RegisterUserRes, error) {
	// 1. 检查公司名称是否已存在
	companyExists, err := dao.Company.Ctx(ctx).Where("name", req.CompanyName).Count()
	if err != nil {
		return nil, err
	}
	if companyExists > 0 {
		return nil, errors.New("公司名称已存在")
	}

	// 2. 检查用户账号是否已存在（全局检查）
	userExists, err := dao.Employee.Ctx(ctx).Where("username", req.Username).Count()
	if err != nil {
		return nil, err
	}
	if userExists > 0 {
		return nil, errors.New("用户账号已存在")
	}

	// 开启事务
	var result *RegisterUserRes
	err = g.DB().Transaction(ctx, func(ctx context.Context, tx gdb.TX) error {
		// 3. 创建公司
		company := &entity.Company{
			Name:      req.CompanyName,
			Status:    1, // 正常状态
			Introduce: "",
			Address:   "",
			Email:     req.Email,
			Telphone:  req.Phone,
			Logo:      "",
		}

		companyResult, err := dao.Company.Ctx(ctx).Data(company).Insert()
		if err != nil {
			return err
		}

		companyId, err := companyResult.LastInsertId()
		if err != nil {
			return err
		}

		// 4. 创建默认部门（总经理办公室）
		department := &entity.Department{
			CompanyId:   uint(companyId),
			ParentId:    0,
			Name:        "总经理办公室",
			Code:        "CEO_OFFICE",
			Description: "公司最高管理层",
			ManagerId:   0, // 稍后更新
			Sort:        1,
			Status:      1,
		}

		deptResult, err := dao.Department.Ctx(ctx).Data(department).Insert()
		if err != nil {
			return err
		}

		deptId, err := deptResult.LastInsertId()
		if err != nil {
			return err
		}

		// 5. 创建超级管理员角色
		role := &entity.Role{
			CompanyId:   uint(companyId),
			Name:        "超级管理员",
			Code:        "SUPER_ADMIN",
			Description: "系统超级管理员，拥有所有权限",
			Type:        1,       // 系统角色
			Permissions: `["*"]`, // 所有权限
			Status:      1,
		}

		roleResult, err := dao.Role.Ctx(ctx).Data(role).Insert()
		if err != nil {
			return err
		}

		roleId, err := roleResult.LastInsertId()
		if err != nil {
			return err
		}

		// 6. 加密密码
		hashedPassword, err := s.hashPassword(req.Password)
		if err != nil {
			return err
		}

		// 7. 创建用户（员工）
		employee := &entity.Employee{
			CompanyId:    uint(companyId),
			DepartmentId: uint(deptId),
			EmployeeNo:   "CEO001", // 默认工号
			Username:     req.Username,
			Password:     hashedPassword,
			RealName:     req.RealName,
			Nickname:     req.RealName,
			Phone:        req.Phone,
			Email:        req.Email,
			Status:       1, // 在职
			EntryDate:    gtime.Now(),
		}

		empResult, err := dao.Employee.Ctx(ctx).Data(employee).Insert()
		if err != nil {
			return err
		}

		empId, err := empResult.LastInsertId()
		if err != nil {
			return err
		}

		// 8. 分配超级管理员角色
		employeeRole := &entity.EmployeeRole{
			EmployeeId: uint(empId),
			RoleId:     uint(roleId),
		}

		_, err = dao.EmployeeRole.Ctx(ctx).Data(employeeRole).Insert()
		if err != nil {
			return err
		}

		// 9. 更新公司的所有者ID
		_, err = dao.Company.Ctx(ctx).Where("id", companyId).Update(g.Map{
			"owner_id": empId,
		})
		if err != nil {
			return err
		}

		// 10. 更新部门经理ID
		_, err = dao.Department.Ctx(ctx).Where("id", deptId).Update(g.Map{
			"manager_id": empId,
		})
		if err != nil {
			return err
		}

		// 设置返回结果
		result = &RegisterUserRes{
			CompanyId: uint(companyId),
			UserId:    uint(empId),
			Username:  req.Username,
			RealName:  req.RealName,
		}

		return nil
	})

	if err != nil {
		return nil, err
	}

	return result, nil
}
