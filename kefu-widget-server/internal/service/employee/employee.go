package employee

import (
	"context"
	"errors"
	"kefu-server/internal/dao"
	"kefu-server/internal/model/entity"

	"github.com/gogf/gf/v2/crypto/gmd5"
	"github.com/gogf/gf/v2/frame/g"
	"github.com/gogf/gf/v2/util/grand"
)

type Service struct{}

func New() *Service {
	return &Service{}
}

// CreateEmployee 创建员工
func (s *Service) CreateEmployee(ctx context.Context, req *CreateEmployeeReq) (*entity.Employee, error) {
	// 检查用户名是否已存在
	exists, err := dao.Employee.Ctx(ctx).Where("username", req.Username).Where("company_id", req.CompanyId).Count()
	if err != nil {
		return nil, err
	}
	if exists > 0 {
		return nil, errors.New("用户名已存在")
	}

	// 检查工号是否已存在
	if req.EmployeeNo != "" {
		exists, err := dao.Employee.Ctx(ctx).Where("employee_no", req.EmployeeNo).Where("company_id", req.CompanyId).Count()
		if err != nil {
			return nil, err
		}
		if exists > 0 {
			return nil, errors.New("工号已存在")
		}
	}

	// 检查部门是否存在
	if req.DepartmentId > 0 {
		dept, err := dao.Department.Ctx(ctx).Where("id", req.DepartmentId).Where("company_id", req.CompanyId).One()
		if err != nil {
			return nil, err
		}
		if dept.IsEmpty() {
			return nil, errors.New("部门不存在")
		}
	}

	// 加密密码
	hashedPassword, err := s.hashPassword(req.Password)
	if err != nil {
		return nil, err
	}

	// 创建员工
	employee := &entity.Employee{
		CompanyId:    req.CompanyId,
		DepartmentId: req.DepartmentId,
		EmployeeNo:   req.EmployeeNo,
		Username:     req.Username,
		Password:     hashedPassword,
		RealName:     req.RealName,
		Nickname:     req.Nickname,
		Avatar:       req.Avatar,
		Gender:       req.Gender,
		Birthday:     req.Birthday,
		Phone:        req.Phone,
		Email:        req.Email,
		IdCard:       req.IdCard,
		Address:      req.Address,
		EntryDate:    req.EntryDate,
		Position:     req.Position,
		Level:        req.Level,
		Salary:       req.Salary,
		Status:       1, // 默认在职
		Remark:       req.Remark,
	}

	result, err := dao.Employee.Ctx(ctx).Data(employee).Insert()
	if err != nil {
		return nil, err
	}

	id, err := result.LastInsertId()
	if err != nil {
		return nil, err
	}

	employee.Id = uint(id)

	// 分配角色
	if len(req.RoleIds) > 0 {
		err = s.assignRoles(ctx, employee.Id, req.RoleIds)
		if err != nil {
			return nil, err
		}
	}

	return employee, nil
}

// UpdateEmployee 更新员工
func (s *Service) UpdateEmployee(ctx context.Context, req *UpdateEmployeeReq) error {
	// 检查员工是否存在
	exists, err := dao.Employee.Ctx(ctx).Where("id", req.Id).Where("company_id", req.CompanyId).Count()
	if err != nil {
		return err
	}
	if exists == 0 {
		return g.Error("员工不存在")
	}

	// 检查用户名是否已被其他员工使用
	if req.Username != "" {
		count, err := dao.Employee.Ctx(ctx).Where("username", req.Username).Where("company_id", req.CompanyId).Where("id !=", req.Id).Count()
		if err != nil {
			return err
		}
		if count > 0 {
			return g.Error("用户名已存在")
		}
	}

	// 检查工号是否已被其他员工使用
	if req.EmployeeNo != "" {
		count, err := dao.Employee.Ctx(ctx).Where("employee_no", req.EmployeeNo).Where("company_id", req.CompanyId).Where("id !=", req.Id).Count()
		if err != nil {
			return err
		}
		if count > 0 {
			return g.Error("工号已存在")
		}
	}

	// 更新员工信息
	updateData := g.Map{}
	if req.DepartmentId > 0 {
		updateData["department_id"] = req.DepartmentId
	}
	if req.EmployeeNo != "" {
		updateData["employee_no"] = req.EmployeeNo
	}
	if req.Username != "" {
		updateData["username"] = req.Username
	}
	if req.Password != "" {
		hashedPassword, err := s.hashPassword(req.Password)
		if err != nil {
			return err
		}
		updateData["password"] = hashedPassword
	}
	if req.RealName != "" {
		updateData["real_name"] = req.RealName
	}
	if req.Nickname != "" {
		updateData["nickname"] = req.Nickname
	}
	if req.Avatar != "" {
		updateData["avatar"] = req.Avatar
	}
	if req.Gender >= 0 {
		updateData["gender"] = req.Gender
	}
	if req.Birthday != nil {
		updateData["birthday"] = req.Birthday
	}
	if req.Phone != "" {
		updateData["phone"] = req.Phone
	}
	if req.Email != "" {
		updateData["email"] = req.Email
	}
	if req.IdCard != "" {
		updateData["id_card"] = req.IdCard
	}
	if req.Address != "" {
		updateData["address"] = req.Address
	}
	if req.EntryDate != nil {
		updateData["entry_date"] = req.EntryDate
	}
	if req.LeaveDate != nil {
		updateData["leave_date"] = req.LeaveDate
	}
	if req.Position != "" {
		updateData["position"] = req.Position
	}
	if req.Level != "" {
		updateData["level"] = req.Level
	}
	if req.Salary > 0 {
		updateData["salary"] = req.Salary
	}
	if req.Status >= 0 {
		updateData["status"] = req.Status
	}
	if req.Remark != "" {
		updateData["remark"] = req.Remark
	}

	_, err = dao.Employee.Ctx(ctx).Where("id", req.Id).Where("company_id", req.CompanyId).Update(updateData)
	if err != nil {
		return err
	}

	// 更新角色
	if len(req.RoleIds) > 0 {
		// 先删除原有角色
		_, err = dao.EmployeeRole.Ctx(ctx).Where("employee_id", req.Id).Delete()
		if err != nil {
			return err
		}
		// 重新分配角色
		err = s.assignRoles(ctx, req.Id, req.RoleIds)
		if err != nil {
			return err
		}
	}

	return nil
}

// DeleteEmployee 删除员工
func (s *Service) DeleteEmployee(ctx context.Context, id uint, companyId uint) error {
	// 检查员工是否存在
	employee, err := dao.Employee.Ctx(ctx).Where("id", id).Where("company_id", companyId).One()
	if err != nil {
		return err
	}
	if employee.IsEmpty() {
		return g.Error("员工不存在")
	}

	// 软删除员工
	_, err = dao.Employee.Ctx(ctx).Where("id", id).Where("company_id", companyId).Update(g.Map{
		"status":     0,
		"deleted_at": g.Time(),
	})
	if err != nil {
		return err
	}

	// 删除员工角色关联
	_, err = dao.EmployeeRole.Ctx(ctx).Where("employee_id", id).Delete()
	return err
}

// GetEmployeeById 根据ID获取员工信息
func (s *Service) GetEmployeeById(ctx context.Context, id uint, companyId uint) (*EmployeeWithRoles, error) {
	result, err := dao.Employee.Ctx(ctx).Where("id", id).Where("company_id", companyId).Where("status", 1).One()
	if err != nil {
		return nil, err
	}
	if result.IsEmpty() {
		return nil, nil
	}

	var employee entity.Employee
	if err := result.Struct(&employee); err != nil {
		return nil, err
	}

	// 获取员工角色
	roles, err := s.getEmployeeRoles(ctx, id)
	if err != nil {
		return nil, err
	}

	return &EmployeeWithRoles{
		Employee: &employee,
		Roles:    roles,
	}, nil
}

// GetEmployeeList 获取员工列表
func (s *Service) GetEmployeeList(ctx context.Context, req *GetEmployeeListReq) ([]*EmployeeWithRoles, int, error) {
	query := dao.Employee.Ctx(ctx).Where("company_id", req.CompanyId).Where("status", 1)

	// 添加查询条件
	if req.DepartmentId > 0 {
		query = query.Where("department_id", req.DepartmentId)
	}
	if req.RealName != "" {
		query = query.WhereLike("real_name", "%"+req.RealName+"%")
	}
	if req.Username != "" {
		query = query.WhereLike("username", "%"+req.Username+"%")
	}
	if req.Phone != "" {
		query = query.Where("phone", req.Phone)
	}
	if req.Email != "" {
		query = query.Where("email", req.Email)
	}
	if req.Position != "" {
		query = query.WhereLike("position", "%"+req.Position+"%")
	}

	// 获取总数
	total, err := query.Count()
	if err != nil {
		return nil, 0, err
	}

	// 分页查询
	if req.Page > 0 && req.Limit > 0 {
		offset := (req.Page - 1) * req.Limit
		query = query.Offset(offset).Limit(req.Limit)
	}

	// 排序
	query = query.Order("id DESC")

	results, err := query.All()
	if err != nil {
		return nil, 0, err
	}

	var employees []*EmployeeWithRoles
	for _, result := range results {
		var employee entity.Employee
		if err := result.Struct(&employee); err != nil {
			return nil, 0, err
		}

		// 获取员工角色
		roles, err := s.getEmployeeRoles(ctx, employee.Id)
		if err != nil {
			return nil, 0, err
		}

		employees = append(employees, &EmployeeWithRoles{
			Employee: &employee,
			Roles:    roles,
		})
	}

	return employees, total, nil
}

// assignRoles 分配角色
func (s *Service) assignRoles(ctx context.Context, employeeId uint, roleIds []uint) error {
	for _, roleId := range roleIds {
		employeeRole := &entity.EmployeeRole{
			EmployeeId: employeeId,
			RoleId:     roleId,
		}
		_, err := dao.EmployeeRole.Ctx(ctx).Data(employeeRole).Insert()
		if err != nil {
			return err
		}
	}
	return nil
}

// getEmployeeRoles 获取员工角色
func (s *Service) getEmployeeRoles(ctx context.Context, employeeId uint) ([]*entity.Role, error) {
	results, err := dao.Role.Ctx(ctx).
		LeftJoin("ks_employee_role er", "ks_role.id = er.role_id").
		Where("er.employee_id", employeeId).
		Where("ks_role.status", 1).
		All()
	if err != nil {
		return nil, err
	}

	var roles []*entity.Role
	for _, result := range results {
		var role entity.Role
		if err := result.Struct(&role); err != nil {
			return nil, err
		}
		roles = append(roles, &role)
	}

	return roles, nil
}

// hashPassword 加密密码
func (s *Service) hashPassword(password string) (string, error) {
	salt := grand.S(8)
	return gmd5.MustEncrypt(password + salt), nil
}
