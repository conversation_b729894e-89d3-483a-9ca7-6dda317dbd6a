package entity

import "github.com/gogf/gf/v2/os/gtime"

// SkillGroup 技能组表
type SkillGroup struct {
	Id          uint64      `json:"id" orm:"id,primary"`           // 技能组ID
	CompanyId   uint64      `json:"company_id" orm:"company_id"`   // 公司ID
	Name        string      `json:"name" orm:"name"`               // 技能组名称
	Description string      `json:"description" orm:"description"` // 技能组描述
	Status      int         `json:"status" orm:"status"`           // 状态：1-正常，0-禁用
	CreatedAt   *gtime.Time `json:"created_at" orm:"created_at"`   // 创建时间
	UpdatedAt   *gtime.Time `json:"updated_at" orm:"updated_at"`   // 更新时间
}

// TableName 返回表名
func (sg *SkillGroup) TableName() string {
	return "ks_skill_group"
}

// SkillGroupCustomerService 技能组客服关联表
type SkillGroupCustomerService struct {
	Id                uint64      `json:"id" orm:"id,primary"`                           // ID
	SkillGroupId      uint64      `json:"skill_group_id" orm:"skill_group_id"`           // 技能组ID
	CustomerServiceId uint64      `json:"customer_service_id" orm:"customer_service_id"` // 客服ID
	CreatedAt         *gtime.Time `json:"created_at" orm:"created_at"`                   // 创建时间
}

// TableName 返回表名
func (sgcs *SkillGroupCustomerService) TableName() string {
	return "ks_skill_group_customer_service"
}
