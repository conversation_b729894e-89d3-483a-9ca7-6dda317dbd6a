package entity

import "github.com/gogf/gf/v2/os/gtime"

// InvitePopupSettings 邀请弹窗设置表
type InvitePopupSettings struct {
	Id            uint64      `json:"id" orm:"id,primary"`                 // 设置ID
	SiteId        uint64      `json:"site_id" orm:"site_id"`               // 站点ID
	Enabled       int         `json:"enabled" orm:"enabled"`               // 启用状态：1-启用，0-禁用
	Title         string      `json:"title" orm:"title"`                   // 弹窗标题
	Content       string      `json:"content" orm:"content"`               // 弹窗内容
	ShowDelay     int         `json:"show_delay" orm:"show_delay"`         // 显示延迟(秒)
	ShowFrequency int         `json:"show_frequency" orm:"show_frequency"` // 显示频率：1-每次访问，2-每天一次，3-每周一次
	Position      string      `json:"position" orm:"position"`             // 弹窗位置：center,top,bottom,left,right
	Style         string      `json:"style" orm:"style"`                   // 样式配置(JSON格式)
	CreatedAt     *gtime.Time `json:"created_at" orm:"created_at"`         // 创建时间
	UpdatedAt     *gtime.Time `json:"updated_at" orm:"updated_at"`         // 更新时间
}

// TableName 返回表名
func (ips *InvitePopupSettings) TableName() string {
	return "ks_invite_popup_settings"
}

// ConsultIconSettings 咨询图标设置表
type ConsultIconSettings struct {
	Id        uint64      `json:"id" orm:"id,primary"`         // 设置ID
	SiteId    uint64      `json:"site_id" orm:"site_id"`       // 站点ID
	Enabled   int         `json:"enabled" orm:"enabled"`       // 启用状态：1-启用，0-禁用
	IconType  string      `json:"icon_type" orm:"icon_type"`   // 图标类型：default,custom,image
	IconUrl   string      `json:"icon_url" orm:"icon_url"`     // 图标URL
	Position  string      `json:"position" orm:"position"`     // 位置：bottom-right,bottom-left,top-right,top-left
	OffsetX   int         `json:"offset_x" orm:"offset_x"`     // X轴偏移
	OffsetY   int         `json:"offset_y" orm:"offset_y"`     // Y轴偏移
	Size      string      `json:"size" orm:"size"`             // 尺寸：small,medium,large
	Animation string      `json:"animation" orm:"animation"`   // 动画效果
	Style     string      `json:"style" orm:"style"`           // 样式配置(JSON格式)
	CreatedAt *gtime.Time `json:"created_at" orm:"created_at"` // 创建时间
	UpdatedAt *gtime.Time `json:"updated_at" orm:"updated_at"` // 更新时间
}

// TableName 返回表名
func (cis *ConsultIconSettings) TableName() string {
	return "ks_consult_icon_settings"
}

// QuickChatSettings 快捷对话弹窗设置表
type QuickChatSettings struct {
	Id               uint64      `json:"id" orm:"id,primary"`                         // 设置ID
	SiteId           uint64      `json:"site_id" orm:"site_id"`                       // 站点ID
	Enabled          int         `json:"enabled" orm:"enabled"`                       // 启用状态：1-启用，0-禁用
	Title            string      `json:"title" orm:"title"`                           // 弹窗标题
	Width            int         `json:"width" orm:"width"`                           // 弹窗宽度
	Height           int         `json:"height" orm:"height"`                         // 弹窗高度
	Position         string      `json:"position" orm:"position"`                     // 弹窗位置
	ShowAvatar       int         `json:"show_avatar" orm:"show_avatar"`               // 显示头像：1-显示，0-不显示
	ShowNickname     int         `json:"show_nickname" orm:"show_nickname"`           // 显示昵称：1-显示，0-不显示
	ShowOnlineStatus int         `json:"show_online_status" orm:"show_online_status"` // 显示在线状态：1-显示，0-不显示
	QuickReplies     string      `json:"quick_replies" orm:"quick_replies"`           // 快捷回复(JSON格式)
	Style            string      `json:"style" orm:"style"`                           // 样式配置(JSON格式)
	CreatedAt        *gtime.Time `json:"created_at" orm:"created_at"`                 // 创建时间
	UpdatedAt        *gtime.Time `json:"updated_at" orm:"updated_at"`                 // 更新时间
}

// TableName 返回表名
func (qcs *QuickChatSettings) TableName() string {
	return "ks_quick_chat_settings"
}

// IndependentChatSettings 独立对话窗设置表
type IndependentChatSettings struct {
	Id               uint64      `json:"id" orm:"id,primary"`                         // 设置ID
	SiteId           uint64      `json:"site_id" orm:"site_id"`                       // 站点ID
	Enabled          int         `json:"enabled" orm:"enabled"`                       // 启用状态：1-启用，0-禁用
	WindowTitle      string      `json:"window_title" orm:"window_title"`             // 窗口标题
	Width            int         `json:"width" orm:"width"`                           // 窗口宽度
	Height           int         `json:"height" orm:"height"`                         // 窗口高度
	Resizable        int         `json:"resizable" orm:"resizable"`                   // 可调整大小：1-可以，0-不可以
	ShowToolbar      int         `json:"show_toolbar" orm:"show_toolbar"`             // 显示工具栏：1-显示，0-不显示
	ShowEmoji        int         `json:"show_emoji" orm:"show_emoji"`                 // 显示表情：1-显示，0-不显示
	ShowFileUpload   int         `json:"show_file_upload" orm:"show_file_upload"`     // 显示文件上传：1-显示，0-不显示
	ShowHistory      int         `json:"show_history" orm:"show_history"`             // 显示历史记录：1-显示，0-不显示
	MaxMessageLength int         `json:"max_message_length" orm:"max_message_length"` // 最大消息长度
	Style            string      `json:"style" orm:"style"`                           // 样式配置(JSON格式)
	CreatedAt        *gtime.Time `json:"created_at" orm:"created_at"`                 // 创建时间
	UpdatedAt        *gtime.Time `json:"updated_at" orm:"updated_at"`                 // 更新时间
}

// TableName 返回表名
func (ics *IndependentChatSettings) TableName() string {
	return "ks_independent_chat_settings"
}

// VisitorMessageSettings 访客留言设置表
type VisitorMessageSettings struct {
	Id                uint64      `json:"id" orm:"id,primary"`                         // 设置ID
	SiteId            uint64      `json:"site_id" orm:"site_id"`                       // 站点ID
	Enabled           int         `json:"enabled" orm:"enabled"`                       // 启用状态：1-启用，0-禁用
	Title             string      `json:"title" orm:"title"`                           // 留言标题
	RequiredFields    string      `json:"required_fields" orm:"required_fields"`       // 必填字段(JSON格式)
	OptionalFields    string      `json:"optional_fields" orm:"optional_fields"`       // 可选字段(JSON格式)
	MaxMessageLength  int         `json:"max_message_length" orm:"max_message_length"` // 最大留言长度
	AutoReplyEnabled  int         `json:"auto_reply_enabled" orm:"auto_reply_enabled"` // 自动回复启用：1-启用，0-禁用
	AutoReplyMessage  string      `json:"auto_reply_message" orm:"auto_reply_message"` // 自动回复消息
	EmailNotification int         `json:"email_notification" orm:"email_notification"` // 邮件通知：1-启用，0-禁用
	NotificationEmail string      `json:"notification_email" orm:"notification_email"` // 通知邮箱
	Style             string      `json:"style" orm:"style"`                           // 样式配置(JSON格式)
	CreatedAt         *gtime.Time `json:"created_at" orm:"created_at"`                 // 创建时间
	UpdatedAt         *gtime.Time `json:"updated_at" orm:"updated_at"`                 // 更新时间
}

// TableName 返回表名
func (vms *VisitorMessageSettings) TableName() string {
	return "ks_visitor_message_settings"
}
