package entity

import (
	"github.com/gogf/gf/v2/os/gtime"
)

// Role 角色实体
type Role struct {
	Id          uint        `json:"id"          orm:"id,primary"     description:"角色ID"`
	CompanyId   uint        `json:"company_id"  orm:"company_id"     description:"公司ID"`
	Name        string      `json:"name"        orm:"name"           description:"角色名称"`
	Code        string      `json:"code"        orm:"code"           description:"角色编码"`
	Description string      `json:"description" orm:"description"    description:"角色描述"`
	Type        int         `json:"type"        orm:"type"           description:"角色类型：1-系统角色，2-自定义角色"`
	Permissions string      `json:"permissions" orm:"permissions"    description:"权限列表，JSON格式"`
	Status      int         `json:"status"      orm:"status"         description:"状态：1-正常，0-禁用"`
	CreatedAt   *gtime.Time `json:"created_at"  orm:"created_at"     description:"创建时间"`
	UpdatedAt   *gtime.Time `json:"updated_at"  orm:"updated_at"     description:"更新时间"`
	DeletedAt   *gtime.Time `json:"deleted_at"  orm:"deleted_at"     description:"删除时间"`
}

// TableName 返回表名
func (Role) TableName() string {
	return "ks_role"
}

// EmployeeRole 员工角色关联实体
type EmployeeRole struct {
	Id         uint        `json:"id"          orm:"id,primary"     description:"关联ID"`
	EmployeeId uint        `json:"employee_id" orm:"employee_id"    description:"员工ID"`
	RoleId     uint        `json:"role_id"     orm:"role_id"        description:"角色ID"`
	CreatedAt  *gtime.Time `json:"created_at"  orm:"created_at"     description:"创建时间"`
}

// TableName 返回表名
func (EmployeeRole) TableName() string {
	return "ks_employee_role"
}
