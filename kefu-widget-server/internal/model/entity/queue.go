package entity

import "github.com/gogf/gf/v2/os/gtime"

// SessionQueue 会话排队表
type SessionQueue struct {
	Id            uint64      `json:"id" orm:"id,primary"`                 // 排队ID
	SessionId     string      `json:"session_id" orm:"session_id"`         // 会话ID
	SiteId        uint64      `json:"site_id" orm:"site_id"`               // 站点ID
	CompanyId     uint64      `json:"company_id" orm:"company_id"`         // 公司ID
	VisitorId     string      `json:"visitor_id" orm:"visitor_id"`         // 访客ID
	SkillGroupId  uint64      `json:"skill_group_id" orm:"skill_group_id"` // 技能组ID
	Priority      int         `json:"priority" orm:"priority"`             // 优先级
	QueuePosition int         `json:"queue_position" orm:"queue_position"` // 排队位置
	EstimatedWait int         `json:"estimated_wait" orm:"estimated_wait"` // 预估等待时间(秒)
	Status        int         `json:"status" orm:"status"`                 // 排队状态：0-排队中，1-已分配，2-已取消，3-超时
	JoinTime      *gtime.Time `json:"join_time" orm:"join_time"`           // 加入排队时间
	AssignTime    *gtime.Time `json:"assign_time" orm:"assign_time"`       // 分配时间
	LeaveTime     *gtime.Time `json:"leave_time" orm:"leave_time"`         // 离开排队时间
	CreatedAt     *gtime.Time `json:"created_at" orm:"created_at"`         // 创建时间
	UpdatedAt     *gtime.Time `json:"updated_at" orm:"updated_at"`         // 更新时间
}

// TableName 返回表名
func (sq *SessionQueue) TableName() string {
	return "ks_session_queue"
}
