package entity

// 站点-客服座席关联表(多对多关系)
type SiteAgent struct {
	Id       uint64 `json:"id" orm:"id,primary"`
	SiteId   uint64 `json:"site_id" orm:"site_id"`   // 站点ID
	AgentId  uint64 `json:"agent_id" orm:"agent_id"` // 客服ID
	Nickname string `json:"nickname" orm:"nickname"` // 昵称
	Avatar   string `json:"avatar" orm:"avatar"`     // 头像
	Ext      string `json:"ext" orm:"ext"`           // 扩展信息 Key-Value的JSON格式
}

// TableName 返回表名
func (s *SiteAgent) TableName() string {
	return "ks_site_agent"
}
