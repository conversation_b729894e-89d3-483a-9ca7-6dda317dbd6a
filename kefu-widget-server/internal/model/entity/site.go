package entity

import "github.com/gogf/gf/v2/os/gtime"

// Site 网站站点表
type Site struct {
	Id        uint64      `json:"id" orm:"id,primary"`         // 站点ID
	CompanyId uint64      `json:"company_id" orm:"company_id"` // 公司ID
	Name      string      `json:"name" orm:"name"`             // 站点名称
	Domain    string      `json:"domain" orm:"domain"`         // 站点域名
	SiteKey   string      `json:"site_key" orm:"site_key"`     // 站点密钥
	Status    int         `json:"status" orm:"status"`         // 状态：1-正常，0-禁用
	CreatedAt *gtime.Time `json:"created_at" orm:"created_at"` // 创建时间
	UpdatedAt *gtime.Time `json:"updated_at" orm:"updated_at"` // 更新时间
}

// TableName 返回表名
func (s *Site) TableName() string {
	return "ks_site"
}
