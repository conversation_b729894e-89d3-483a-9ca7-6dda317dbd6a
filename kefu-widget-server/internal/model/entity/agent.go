package entity

import "github.com/gogf/gf/v2/os/gtime"

// Agent 席位表
type Agent struct {
	Id                uint64      `json:"id" orm:"id,primary"`                         // 席位ID
	CompanyId         uint64      `json:"company_id" orm:"company_id"`                 // 公司ID
	SiteId            uint64      `json:"site_id" orm:"site_id"`                       // 站点ID
	SkillGroupId      uint64      `json:"skill_group_id" orm:"skill_group_id"`         // 技能组ID
	EmployeeId        uint64      `json:"employee_id" orm:"employee_id"`               // 员工ID
	Status            int         `json:"status" orm:"status"`                         // 状态：1-在线，2-忙碌，3-离线，0-禁用
	MaxConcurrent     int         `json:"max_concurrent" orm:"max_concurrent"`         // 最大并发接待数
	CurrentConcurrent int         `json:"current_concurrent" orm:"current_concurrent"` // 当前接待数
	LastLoginTime     *gtime.Time `json:"last_login_time" orm:"last_login_time"`       // 最后登录时间
	LastLoginIp       string      `json:"last_login_ip" orm:"last_login_ip"`           // 最后登录IP
	PurchaseTime      *gtime.Time `json:"purchase_time" orm:"purchase_time"`           // 购买时间
	ExpireTime        *gtime.Time `json:"expire_time" orm:"expire_time"`               // 过期时间
	CreatedAt         *gtime.Time `json:"created_at" orm:"created_at"`                 // 创建时间
	UpdatedAt         *gtime.Time `json:"updated_at" orm:"updated_at"`                 // 更新时间
}

// TableName 返回表名
func (a *Agent) TableName() string {
	return "ks_agent"
}
