package entity

import "github.com/gogf/gf/v2/os/gtime"

// CustomerService 客服表
type CustomerService struct {
	Id                uint64      `json:"id" orm:"id,primary"`                         // 客服ID
	CompanyId         uint64      `json:"company_id" orm:"company_id"`                 // 公司ID
	SeatId            uint64      `json:"seat_id" orm:"seat_id"`                       // 座席ID
	Username          string      `json:"username" orm:"username"`                     // 用户名
	Password          string      `json:"password" orm:"password"`                     // 密码
	Nickname          string      `json:"nickname" orm:"nickname"`                     // 昵称
	Avatar            string      `json:"avatar" orm:"avatar"`                         // 头像
	Email             string      `json:"email" orm:"email"`                           // 邮箱
	Phone             string      `json:"phone" orm:"phone"`                           // 电话
	Status            int         `json:"status" orm:"status"`                         // 状态：1-在线，2-忙碌，3-离线，0-禁用
	MaxConcurrent     int         `json:"max_concurrent" orm:"max_concurrent"`         // 最大并发接待数
	CurrentConcurrent int         `json:"current_concurrent" orm:"current_concurrent"` // 当前接待数
	LastLoginTime     *gtime.Time `json:"last_login_time" orm:"last_login_time"`       // 最后登录时间
	LastLoginIp       string      `json:"last_login_ip" orm:"last_login_ip"`           // 最后登录IP
	CreatedAt         *gtime.Time `json:"created_at" orm:"created_at"`                 // 创建时间
	UpdatedAt         *gtime.Time `json:"updated_at" orm:"updated_at"`                 // 更新时间
}

// TableName 返回表名
func (cs *CustomerService) TableName() string {
	return "ks_customer_service"
}
