package entity

import (
	"github.com/gogf/gf/v2/os/gtime"
)

// Company 公司表
type Company struct {
	Id         uint64      `json:"id" orm:"id,primary"`           // 公司ID
	Name       string      `json:"name" orm:"name"`               // 公司名称
	Introduce  string      `json:"introduce" orm:"introduce"`     // 公司简介
	Address    string      `json:"address" orm:"address"`         // 公司地址
	Email      string      `json:"email" orm:"email"`             // 邮箱地址
	Telphone   string      `json:"telphone" orm:"telphone"`       // 固定电话
	Logo       string      `json:"logo" orm:"logo"`               // 公司Logo
	Ownerid    uint64      `json:"owner_id" orm:"owner_id"`       // 超级管理员
	Status     int         `json:"status" orm:"status"`           // 状态：1-正常，0-禁用
	ExpireTime *gtime.Time `json:"expire_time" orm:"expire_time"` // 过期时间
	CreatedAt  *gtime.Time `json:"created_at" orm:"created_at"`   // 创建时间
	UpdatedAt  *gtime.Time `json:"updated_at" orm:"updated_at"`   // 更新时间
}

// TableName 返回表名
func (c *Company) TableName() string {
	return "ks_company"
}

// Seat 座席表
type Seat struct {
	Id           uint64      `json:"id" orm:"id,primary"`               // 座席ID
	CompanyId    uint64      `json:"company_id" orm:"company_id"`       // 公司ID
	Name         string      `json:"name" orm:"name"`                   // 座席名称
	MaxDuration  int         `json:"max_duration" orm:"max_duration"`   // 最大时长(分钟)
	UsedDuration int         `json:"used_duration" orm:"used_duration"` // 已使用时长(分钟)
	Status       int         `json:"status" orm:"status"`               // 状态：1-正常，0-禁用
	ExpireTime   *gtime.Time `json:"expire_time" orm:"expire_time"`     // 过期时间
	CreatedAt    *gtime.Time `json:"created_at" orm:"created_at"`       // 创建时间
	UpdatedAt    *gtime.Time `json:"updated_at" orm:"updated_at"`       // 更新时间
}

// TableName 返回表名
func (s *Seat) TableName() string {
	return "ks_seat"
}

// Site 网站站点表
type Site struct {
	Id        uint64      `json:"id" orm:"id,primary"`         // 站点ID
	CompanyId uint64      `json:"company_id" orm:"company_id"` // 公司ID
	Name      string      `json:"name" orm:"name"`             // 站点名称
	Domain    string      `json:"domain" orm:"domain"`         // 站点域名
	SiteKey   string      `json:"site_key" orm:"site_key"`     // 站点密钥
	Status    int         `json:"status" orm:"status"`         // 状态：1-正常，0-禁用
	CreatedAt *gtime.Time `json:"created_at" orm:"created_at"` // 创建时间
	UpdatedAt *gtime.Time `json:"updated_at" orm:"updated_at"` // 更新时间
}

// TableName 返回表名
func (s *Site) TableName() string {
	return "ks_site"
}

// SiteSettings 网站站点设置表
type SiteSettings struct {
	Id               uint64      `json:"id" orm:"id,primary"`                         // 设置ID
	SiteId           uint64      `json:"site_id" orm:"site_id"`                       // 站点ID
	WelcomeMessage   string      `json:"welcome_message" orm:"welcome_message"`       // 欢迎消息
	OfflineMessage   string      `json:"offline_message" orm:"offline_message"`       // 离线消息
	AutoReplyEnabled int         `json:"auto_reply_enabled" orm:"auto_reply_enabled"` // 自动回复启用：1-是，0-否
	AutoReplyMessage string      `json:"auto_reply_message" orm:"auto_reply_message"` // 自动回复消息
	WorkingHours     string      `json:"working_hours" orm:"working_hours"`           // 工作时间(JSON格式)
	CreatedAt        *gtime.Time `json:"created_at" orm:"created_at"`                 // 创建时间
	UpdatedAt        *gtime.Time `json:"updated_at" orm:"updated_at"`                 // 更新时间
}

// TableName 返回表名
func (ss *SiteSettings) TableName() string {
	return "ks_site_settings"
}
