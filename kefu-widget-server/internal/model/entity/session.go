package entity

import "github.com/gogf/gf/v2/os/gtime"

// VisitorSession 访客会话表
type VisitorSession struct {
	Id                  uint64      `json:"id" orm:"id,primary"`                               // 会话ID
	SessionId           string      `json:"session_id" orm:"session_id"`                       // 会话唯一标识
	SiteId              uint64      `json:"site_id" orm:"site_id"`                             // 站点ID
	CompanyId           uint64      `json:"company_id" orm:"company_id"`                       // 公司ID
	VisitorId           string      `json:"visitor_id" orm:"visitor_id"`                       // 访客唯一标识
	SeatId              uint64      `json:"seat_id" orm:"seat_id"`                             // 分配的座席ID
	CustomerServiceId   uint64      `json:"customer_service_id" orm:"customer_service_id"`     // 分配的客服ID
	SkillGroupId        uint64      `json:"skill_group_id" orm:"skill_group_id"`               // 技能组ID
	Status              int         `json:"status" orm:"status"`                               // 会话状态：0-等待中，1-进行中，2-已结束，3-已转接，4-超时结束
	Priority            int         `json:"priority" orm:"priority"`                           // 优先级：1-低，2-中，3-高，4-紧急
	Source              string      `json:"source" orm:"source"`                               // 来源：web,mobile,api
	VisitorInfo         string      `json:"visitor_info" orm:"visitor_info"`                   // 访客信息(JSON格式)
	StartTime           *gtime.Time `json:"start_time" orm:"start_time"`                       // 会话开始时间
	EndTime             *gtime.Time `json:"end_time" orm:"end_time"`                           // 会话结束时间
	FirstResponseTime   *gtime.Time `json:"first_response_time" orm:"first_response_time"`     // 首次响应时间
	LastActivityTime    *gtime.Time `json:"last_activity_time" orm:"last_activity_time"`       // 最后活动时间
	Duration            int         `json:"duration" orm:"duration"`                           // 会话持续时间(秒)
	MessageCount        int         `json:"message_count" orm:"message_count"`                 // 消息总数
	VisitorMessageCount int         `json:"visitor_message_count" orm:"visitor_message_count"` // 访客消息数
	ServiceMessageCount int         `json:"service_message_count" orm:"service_message_count"` // 客服消息数
	QueueTime           int         `json:"queue_time" orm:"queue_time"`                       // 排队时间(秒)
	WaitTime            int         `json:"wait_time" orm:"wait_time"`                         // 等待时间(秒)
	Satisfaction        int         `json:"satisfaction" orm:"satisfaction"`                   // 满意度评分：1-5分
	SatisfactionComment string      `json:"satisfaction_comment" orm:"satisfaction_comment"`   // 满意度评价
	Tags                string      `json:"tags" orm:"tags"`                                   // 标签(JSON格式)
	Remarks             string      `json:"remarks" orm:"remarks"`                             // 备注
	TransferReason      string      `json:"transfer_reason" orm:"transfer_reason"`             // 转接原因
	EndReason           string      `json:"end_reason" orm:"end_reason"`                       // 结束原因
	CreatedAt           *gtime.Time `json:"created_at" orm:"created_at"`                       // 创建时间
	UpdatedAt           *gtime.Time `json:"updated_at" orm:"updated_at"`                       // 更新时间
}

// TableName 返回表名
func (vs *VisitorSession) TableName() string {
	return "ks_visitor_session"
}
