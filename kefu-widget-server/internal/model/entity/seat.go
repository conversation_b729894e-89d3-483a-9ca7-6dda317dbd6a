package entity

import "github.com/gogf/gf/v2/os/gtime"

// Seat 座席表
type Seat struct {
	Id           uint64      `json:"id" orm:"id,primary"`               // 座席ID
	CompanyId    uint64      `json:"company_id" orm:"company_id"`       // 公司ID
	Name         string      `json:"name" orm:"name"`                   // 座席名称
	MaxDuration  int         `json:"max_duration" orm:"max_duration"`   // 最大时长(分钟)
	UsedDuration int         `json:"used_duration" orm:"used_duration"` // 已使用时长(分钟)
	Status       int         `json:"status" orm:"status"`               // 状态：1-正常，0-禁用
	ExpireTime   *gtime.Time `json:"expire_time" orm:"expire_time"`     // 过期时间
	CreatedAt    *gtime.Time `json:"created_at" orm:"created_at"`       // 创建时间
	UpdatedAt    *gtime.Time `json:"updated_at" orm:"updated_at"`       // 更新时间
}

// TableName 返回表名
func (s *Seat) TableName() string {
	return "ks_seat"
}
