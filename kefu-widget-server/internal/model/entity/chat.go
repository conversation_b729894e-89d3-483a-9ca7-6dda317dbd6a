package entity

import "github.com/gogf/gf/v2/os/gtime"

// ChatMessage 对话消息表
type ChatMessage struct {
	Id                uint64      `json:"id" orm:"id,primary"`                           // 消息ID
	SiteId            uint64      `json:"site_id" orm:"site_id"`                         // 站点ID
	SessionId         string      `json:"session_id" orm:"session_id"`                   // 会话ID
	VisitorId         string      `json:"visitor_id" orm:"visitor_id"`                   // 访客ID
	CustomerServiceId uint64      `json:"customer_service_id" orm:"customer_service_id"` // 客服ID
	MessageType       int         `json:"message_type" orm:"message_type"`               // 消息类型：1-文本，2-图片，3-文件，4-系统消息
	SenderType        int         `json:"sender_type" orm:"sender_type"`                 // 发送者类型：1-访客，2-客服，3-系统
	Content           string      `json:"content" orm:"content"`                         // 消息内容
	FileUrl           string      `json:"file_url" orm:"file_url"`                       // 文件URL
	FileName          string      `json:"file_name" orm:"file_name"`                     // 文件名
	FileSize          int64       `json:"file_size" orm:"file_size"`                     // 文件大小
	IsRead            int         `json:"is_read" orm:"is_read"`                         // 是否已读：1-已读，0-未读
	ReadTime          *gtime.Time `json:"read_time" orm:"read_time"`                     // 阅读时间
	CreatedAt         *gtime.Time `json:"created_at" orm:"created_at"`                   // 创建时间
}

// TableName 返回表名
func (cm *ChatMessage) TableName() string {
	return "ks_chat_message"
}
