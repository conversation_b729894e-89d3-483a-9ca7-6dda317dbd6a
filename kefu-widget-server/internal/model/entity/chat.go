package entity

import "github.com/gogf/gf/v2/os/gtime"

// ChatMessage 对话消息表
type ChatMessage struct {
	Id                uint64      `json:"id" orm:"id,primary"`                           // 消息ID
	SiteId            uint64      `json:"site_id" orm:"site_id"`                         // 站点ID
	SessionId         string      `json:"session_id" orm:"session_id"`                   // 会话ID
	VisitorId         string      `json:"visitor_id" orm:"visitor_id"`                   // 访客ID
	SeatId            uint64      `json:"seat_id" orm:"seat_id"`                         // 座席ID
	CustomerServiceId uint64      `json:"customer_service_id" orm:"customer_service_id"` // 客服ID
	MessageType       int         `json:"message_type" orm:"message_type"`               // 消息类型：1-文本，2-图片，3-文件，4-系统消息
	SenderType        int         `json:"sender_type" orm:"sender_type"`                 // 发送者类型：1-访客，2-客服，3-系统
	Content           string      `json:"content" orm:"content"`                         // 消息内容
	FileUrl           string      `json:"file_url" orm:"file_url"`                       // 文件URL
	FileName          string      `json:"file_name" orm:"file_name"`                     // 文件名
	FileSize          int64       `json:"file_size" orm:"file_size"`                     // 文件大小
	IsRead            int         `json:"is_read" orm:"is_read"`                         // 是否已读：1-已读，0-未读
	ReadTime          *gtime.Time `json:"read_time" orm:"read_time"`                     // 阅读时间
	CreatedAt         *gtime.Time `json:"created_at" orm:"created_at"`                   // 创建时间
}

// TableName 返回表名
func (cm *ChatMessage) TableName() string {
	return "ks_chat_message"
}

// SkillGroup 技能组表
type SkillGroup struct {
	Id          uint64      `json:"id" orm:"id,primary"`           // 技能组ID
	CompanyId   uint64      `json:"company_id" orm:"company_id"`   // 公司ID
	Name        string      `json:"name" orm:"name"`               // 技能组名称
	Description string      `json:"description" orm:"description"` // 技能组描述
	Status      int         `json:"status" orm:"status"`           // 状态：1-正常，0-禁用
	CreatedAt   *gtime.Time `json:"created_at" orm:"created_at"`   // 创建时间
	UpdatedAt   *gtime.Time `json:"updated_at" orm:"updated_at"`   // 更新时间
}

// TableName 返回表名
func (sg *SkillGroup) TableName() string {
	return "ks_skill_group"
}

// CustomerService 客服表
type CustomerService struct {
	Id                uint64      `json:"id" orm:"id,primary"`                         // 客服ID
	CompanyId         uint64      `json:"company_id" orm:"company_id"`                 // 公司ID
	SeatId            uint64      `json:"seat_id" orm:"seat_id"`                       // 座席ID
	Username          string      `json:"username" orm:"username"`                     // 用户名
	Password          string      `json:"password" orm:"password"`                     // 密码
	Nickname          string      `json:"nickname" orm:"nickname"`                     // 昵称
	Avatar            string      `json:"avatar" orm:"avatar"`                         // 头像
	Email             string      `json:"email" orm:"email"`                           // 邮箱
	Phone             string      `json:"phone" orm:"phone"`                           // 电话
	Status            int         `json:"status" orm:"status"`                         // 状态：1-在线，2-忙碌，3-离线，0-禁用
	MaxConcurrent     int         `json:"max_concurrent" orm:"max_concurrent"`         // 最大并发接待数
	CurrentConcurrent int         `json:"current_concurrent" orm:"current_concurrent"` // 当前接待数
	LastLoginTime     *gtime.Time `json:"last_login_time" orm:"last_login_time"`       // 最后登录时间
	LastLoginIp       string      `json:"last_login_ip" orm:"last_login_ip"`           // 最后登录IP
	CreatedAt         *gtime.Time `json:"created_at" orm:"created_at"`                 // 创建时间
	UpdatedAt         *gtime.Time `json:"updated_at" orm:"updated_at"`                 // 更新时间
}

// TableName 返回表名
func (cs *CustomerService) TableName() string {
	return "ks_customer_service"
}

// SeatAllocation 座席分配表
type SeatAllocation struct {
	Id        uint64      `json:"id" orm:"id,primary"`         // ID
	SiteId    uint64      `json:"site_id" orm:"site_id"`       // 站点ID
	SeatId    uint64      `json:"seat_id" orm:"seat_id"`       // 座席ID
	Priority  int         `json:"priority" orm:"priority"`     // 优先级
	Status    int         `json:"status" orm:"status"`         // 状态：1-正常，0-禁用
	CreatedAt *gtime.Time `json:"created_at" orm:"created_at"` // 创建时间
	UpdatedAt *gtime.Time `json:"updated_at" orm:"updated_at"` // 更新时间
}

// TableName 返回表名
func (sa *SeatAllocation) TableName() string {
	return "ks_seat_allocation"
}

// SkillGroupCustomerService 技能组客服关联表
type SkillGroupCustomerService struct {
	Id                uint64      `json:"id" orm:"id,primary"`                           // ID
	SkillGroupId      uint64      `json:"skill_group_id" orm:"skill_group_id"`           // 技能组ID
	CustomerServiceId uint64      `json:"customer_service_id" orm:"customer_service_id"` // 客服ID
	CreatedAt         *gtime.Time `json:"created_at" orm:"created_at"`                   // 创建时间
}

// TableName 返回表名
func (sgcs *SkillGroupCustomerService) TableName() string {
	return "ks_skill_group_customer_service"
}
