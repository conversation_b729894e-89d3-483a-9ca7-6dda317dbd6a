package entity

import "github.com/gogf/gf/v2/os/gtime"

// VisitorBrowseRecord 访客浏览记录表
type VisitorBrowseRecord struct {
	Id        uint64      `json:"id" orm:"id,primary"`         // ID
	SiteId    uint64      `json:"site_id" orm:"site_id"`       // 站点ID
	VisitorId string      `json:"visitor_id" orm:"visitor_id"` // 访客唯一标识
	SessionId string      `json:"session_id" orm:"session_id"` // 会话ID
	PageUrl   string      `json:"page_url" orm:"page_url"`     // 页面URL
	PageTitle string      `json:"page_title" orm:"page_title"` // 页面标题
	Referrer  string      `json:"referrer" orm:"referrer"`     // 来源页面
	StayTime  int         `json:"stay_time" orm:"stay_time"`   // 停留时间(秒)
	VisitTime *gtime.Time `json:"visit_time" orm:"visit_time"` // 访问时间
	CreatedAt *gtime.Time `json:"created_at" orm:"created_at"` // 创建时间
}

// TableName 返回表名
func (vbr *VisitorBrowseRecord) TableName() string {
	return "ks_visitor_browse_record"
}
