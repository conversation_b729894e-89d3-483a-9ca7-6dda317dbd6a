package dao

import (
	"kefu-server/internal/dao/internal"
)

// 数据访问对象实例
var (
	Company                 = internal.NewCompanyDao()
	Site                    = internal.NewSiteDao()
	SiteSettings            = internal.NewSiteSettingsDao()
	InvitePopupSettings     = internal.NewInvitePopupSettingsDao()
	ConsultIconSettings     = internal.NewConsultIconSettingsDao()
	QuickChatSettings       = internal.NewQuickChatSettingsDao()
	IndependentChatSettings = internal.NewIndependentChatSettingsDao()
	VisitorMessageSettings  = internal.NewVisitorMessageSettingsDao()
	Visitor                 = internal.NewVisitorDao()
	VisitorHistory          = internal.NewVisitorHistoryDao()
	VisitorBrowseRecord     = internal.NewVisitorBrowseRecordDao()
	ChatMessage             = internal.NewChatMessageDao()
	SkillGroup              = internal.NewSkillGroupDao()
	Agent                   = internal.NewAgentDao()
	SkillGroupAgent         = internal.NewSkillGroupAgentDao()
	// 会话相关DAO
	VisitorSession    = internal.NewVisitorSessionDao()
	SessionQueue      = internal.NewSessionQueueDao()
	SessionTransfer   = internal.NewSessionTransferDao()
	SessionEvaluation = internal.NewSessionEvaluationDao()
	// 组织架构相关DAO
	Department   = internal.NewDepartmentDao()
	Employee     = internal.NewEmployeeDao()
	Role         = internal.NewRoleDao()
	EmployeeRole = internal.NewEmployeeRoleDao()
	// 邀请记录相关DAO
	InviteRecord = internal.NewInviteRecordDao()
)
