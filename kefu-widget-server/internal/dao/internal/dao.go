package internal

import (
	"context"

	"github.com/gogf/gf/v2/database/gdb"
	"github.com/gogf/gf/v2/frame/g"
)

// BaseDao 基础DAO
type BaseDao struct {
	table string
	group string
}

// DB 获取数据库连接
func (d *BaseDao) DB() gdb.DB {
	return g.DB(d.group)
}

// Ctx 设置上下文
func (d *BaseDao) Ctx(ctx context.Context) *gdb.Model {
	return d.DB().Model(d.table).Ctx(ctx)
}

// CompanyDao 公司DAO
type CompanyDao struct {
	*BaseDao
}

func NewCompanyDao() *CompanyDao {
	return &CompanyDao{
		BaseDao: &BaseDao{
			table: "ks_company",
			group: "default",
		},
	}
}

// SeatDao 座席DAO
type SeatDao struct {
	*BaseDao
}

func NewSeatDao() *SeatDao {
	return &SeatDao{
		BaseDao: &BaseDao{
			table: "ks_seat",
			group: "default",
		},
	}
}

// SiteDao 站点DAO
type SiteDao struct {
	*BaseDao
}

func NewSiteDao() *SiteDao {
	return &SiteDao{
		BaseDao: &BaseDao{
			table: "ks_site",
			group: "default",
		},
	}
}

// SiteSettingsDao 站点设置DAO
type SiteSettingsDao struct {
	*BaseDao
}

func NewSiteSettingsDao() *SiteSettingsDao {
	return &SiteSettingsDao{
		BaseDao: &BaseDao{
			table: "ks_site_settings",
			group: "default",
		},
	}
}

// InvitePopupSettingsDao 邀请弹窗设置DAO
type InvitePopupSettingsDao struct {
	*BaseDao
}

func NewInvitePopupSettingsDao() *InvitePopupSettingsDao {
	return &InvitePopupSettingsDao{
		BaseDao: &BaseDao{
			table: "ks_invite_popup_settings",
			group: "default",
		},
	}
}

// ConsultIconSettingsDao 咨询图标设置DAO
type ConsultIconSettingsDao struct {
	*BaseDao
}

func NewConsultIconSettingsDao() *ConsultIconSettingsDao {
	return &ConsultIconSettingsDao{
		BaseDao: &BaseDao{
			table: "ks_consult_icon_settings",
			group: "default",
		},
	}
}

// QuickChatSettingsDao 快捷对话设置DAO
type QuickChatSettingsDao struct {
	*BaseDao
}

func NewQuickChatSettingsDao() *QuickChatSettingsDao {
	return &QuickChatSettingsDao{
		BaseDao: &BaseDao{
			table: "ks_quick_chat_settings",
			group: "default",
		},
	}
}

// IndependentChatSettingsDao 独立对话设置DAO
type IndependentChatSettingsDao struct {
	*BaseDao
}

func NewIndependentChatSettingsDao() *IndependentChatSettingsDao {
	return &IndependentChatSettingsDao{
		BaseDao: &BaseDao{
			table: "ks_independent_chat_settings",
			group: "default",
		},
	}
}

// VisitorMessageSettingsDao 访客留言设置DAO
type VisitorMessageSettingsDao struct {
	*BaseDao
}

func NewVisitorMessageSettingsDao() *VisitorMessageSettingsDao {
	return &VisitorMessageSettingsDao{
		BaseDao: &BaseDao{
			table: "ks_visitor_message_settings",
			group: "default",
		},
	}
}

// VisitorDao 访客DAO
type VisitorDao struct {
	*BaseDao
}

func NewVisitorDao() *VisitorDao {
	return &VisitorDao{
		BaseDao: &BaseDao{
			table: "ks_visitor",
			group: "default",
		},
	}
}

// OnlineVisitorDao 在线访客DAO
type OnlineVisitorDao struct {
	*BaseDao
}

func NewOnlineVisitorDao() *OnlineVisitorDao {
	return &OnlineVisitorDao{
		BaseDao: &BaseDao{
			table: "ks_online_visitor",
			group: "default",
		},
	}
}

// VisitorHistoryDao 访客历史DAO
type VisitorHistoryDao struct {
	*BaseDao
}

func NewVisitorHistoryDao() *VisitorHistoryDao {
	return &VisitorHistoryDao{
		BaseDao: &BaseDao{
			table: "ks_visitor_history",
			group: "default",
		},
	}
}

// VisitorBrowseRecordDao 访客浏览记录DAO
type VisitorBrowseRecordDao struct {
	*BaseDao
}

func NewVisitorBrowseRecordDao() *VisitorBrowseRecordDao {
	return &VisitorBrowseRecordDao{
		BaseDao: &BaseDao{
			table: "ks_visitor_browse_record",
			group: "default",
		},
	}
}

// ChatMessageDao 聊天消息DAO
type ChatMessageDao struct {
	*BaseDao
}

func NewChatMessageDao() *ChatMessageDao {
	return &ChatMessageDao{
		BaseDao: &BaseDao{
			table: "ks_chat_message",
			group: "default",
		},
	}
}

// SkillGroupDao 技能组DAO
type SkillGroupDao struct {
	*BaseDao
}

func NewSkillGroupDao() *SkillGroupDao {
	return &SkillGroupDao{
		BaseDao: &BaseDao{
			table: "ks_skill_group",
			group: "default",
		},
	}
}

// CustomerServiceDao 客服DAO
type CustomerServiceDao struct {
	*BaseDao
}

func NewCustomerServiceDao() *CustomerServiceDao {
	return &CustomerServiceDao{
		BaseDao: &BaseDao{
			table: "ks_customer_service",
			group: "default",
		},
	}
}

// SeatAllocationDao 座席分配DAO
type SeatAllocationDao struct {
	*BaseDao
}

func NewSeatAllocationDao() *SeatAllocationDao {
	return &SeatAllocationDao{
		BaseDao: &BaseDao{
			table: "ks_seat_allocation",
			group: "default",
		},
	}
}

// SkillGroupCustomerServiceDao 技能组客服关联DAO
type SkillGroupCustomerServiceDao struct {
	*BaseDao
}

func NewSkillGroupCustomerServiceDao() *SkillGroupCustomerServiceDao {
	return &SkillGroupCustomerServiceDao{
		BaseDao: &BaseDao{
			table: "ks_skill_group_customer_service",
			group: "default",
		},
	}
}

// VisitorSessionDao 访客会话DAO
type VisitorSessionDao struct {
	*BaseDao
}

func NewVisitorSessionDao() *VisitorSessionDao {
	return &VisitorSessionDao{
		BaseDao: &BaseDao{
			table: "ks_visitor_session",
			group: "default",
		},
	}
}

// SessionQueueDao 会话排队DAO
type SessionQueueDao struct {
	*BaseDao
}

func NewSessionQueueDao() *SessionQueueDao {
	return &SessionQueueDao{
		BaseDao: &BaseDao{
			table: "ks_session_queue",
			group: "default",
		},
	}
}

// SessionTransferDao 会话转接DAO
type SessionTransferDao struct {
	*BaseDao
}

func NewSessionTransferDao() *SessionTransferDao {
	return &SessionTransferDao{
		BaseDao: &BaseDao{
			table: "ks_session_transfer",
			group: "default",
		},
	}
}

// SessionEvaluationDao 会话评价DAO
type SessionEvaluationDao struct {
	*BaseDao
}

func NewSessionEvaluationDao() *SessionEvaluationDao {
	return &SessionEvaluationDao{
		BaseDao: &BaseDao{
			table: "ks_session_evaluation",
			group: "default",
		},
	}
}

// DepartmentDao 部门DAO
type DepartmentDao struct {
	*BaseDao
}

func NewDepartmentDao() *DepartmentDao {
	return &DepartmentDao{
		BaseDao: &BaseDao{
			table: "ks_department",
			group: "default",
		},
	}
}

// EmployeeDao 员工DAO
type EmployeeDao struct {
	*BaseDao
}

func NewEmployeeDao() *EmployeeDao {
	return &EmployeeDao{
		BaseDao: &BaseDao{
			table: "ks_employee",
			group: "default",
		},
	}
}

// RoleDao 角色DAO
type RoleDao struct {
	*BaseDao
}

func NewRoleDao() *RoleDao {
	return &RoleDao{
		BaseDao: &BaseDao{
			table: "ks_role",
			group: "default",
		},
	}
}

// EmployeeRoleDao 员工角色关联DAO
type EmployeeRoleDao struct {
	*BaseDao
}

func NewEmployeeRoleDao() *EmployeeRoleDao {
	return &EmployeeRoleDao{
		BaseDao: &BaseDao{
			table: "ks_employee_role",
			group: "default",
		},
	}
}
