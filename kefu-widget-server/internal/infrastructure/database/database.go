package database

import (
	"context"
	"errors"
	"fmt"
	"kefu-server/internal/model/entity"
	"reflect"
	"strings"

	_ "github.com/gogf/gf/contrib/drivers/mysql/v2"
	"github.com/gogf/gf/v2/database/gdb"
	"github.com/gogf/gf/v2/frame/g"
	"github.com/gogf/gf/v2/os/glog"
)

var DB gdb.DB

func Init(ctx context.Context) {
	DB = g.DB()

	// 自动迁移数据库表
	if err := autoMigrate(ctx); err != nil {
		g.Log().Fatal(ctx, "数据库迁移失败:", err)
	}

	g.Log().Info(ctx, "数据库初始化完成")
}

func autoMigrate(ctx context.Context) error {
	// 创建所有表
	tables := []interface{}{
		&entity.Company{},
		&entity.Site{},
		&entity.SiteSettings{},
		&entity.InvitePopupSettings{},
		&entity.ConsultIconSettings{},
		&entity.QuickChatSettings{},
		&entity.IndependentChatSettings{},
		&entity.VisitorMessageSettings{},
		&entity.Visitor{},
		&entity.OnlineVisitor{},
		&entity.VisitorHistory{},
		&entity.VisitorBrowseRecord{},
		&entity.ChatMessage{},
		&entity.SkillGroup{},
		&entity.CustomerService{},
		&entity.SkillGroupCustomerService{},
		// 组织架构相关实体
		&entity.Department{},
		&entity.Employee{},
		&entity.Role{},
		&entity.EmployeeRole{},
		// 邀请记录相关实体
		&entity.InviteRecord{},
	}

	for _, table := range tables {
		if err := createTableIfNotExists(ctx, table); err != nil {
			return err
		}
	}

	return nil
}

func createTableIfNotExists(ctx context.Context, model interface{}) error {
	// 使用GoFrame的数据库操作来创建表
	glog.Info(ctx, "检查并创建表:", model)

	// 使用 g.DB() 获取默认数据库连接
	db := g.DB()
	if db == nil {
		return errors.New("数据库连接失败")
	}

	// 使用反射获取表名
	tableName := getTableName(model)
	if tableName == "" {
		return errors.New("无法获取表名")
	}

	// 检查表是否存在
	exists, err := checkTableExists(ctx, db, tableName)
	if err != nil {
		return err
	}

	if !exists {
		// 表不存在，创建表
		glog.Infof(ctx, "创建表: %s", tableName)
		err = createTable(ctx, db, model, tableName)
		if err != nil {
			return err
		}
		glog.Infof(ctx, "表 %s 创建成功", tableName)
	} else {
		glog.Infof(ctx, "表 %s 已存在", tableName)
	}

	return nil
}

// getTableName 获取表名
func getTableName(model interface{}) string {
	// 使用反射获取类型
	t := reflect.TypeOf(model)
	if t.Kind() == reflect.Ptr {
		t = t.Elem()
	}

	// 检查是否有 TableName 方法
	if method := reflect.ValueOf(model).MethodByName("TableName"); method.IsValid() {
		results := method.Call(nil)
		if len(results) > 0 {
			return results[0].String()
		}
	}

	// 如果没有 TableName 方法，使用类型名转换为表名
	typeName := t.Name()
	return toSnakeCase(typeName)
}

// toSnakeCase 将驼峰命名转换为下划线命名
func toSnakeCase(str string) string {
	var result strings.Builder
	for i, r := range str {
		if i > 0 && r >= 'A' && r <= 'Z' {
			result.WriteRune('_')
		}
		result.WriteRune(r)
	}
	return strings.ToLower(result.String())
}

// checkTableExists 检查表是否存在
func checkTableExists(ctx context.Context, db gdb.DB, tableName string) (bool, error) {
	sql := "SELECT COUNT(*) FROM information_schema.tables WHERE table_schema = DATABASE() AND table_name = ?"
	count, err := db.Ctx(ctx).GetCount(ctx, sql, tableName)
	if err != nil {
		return false, err
	}
	return count > 0, nil
}

// createTable 创建表
func createTable(ctx context.Context, db gdb.DB, model interface{}, tableName string) error {
	// 根据不同的实体类型创建对应的表
	switch model.(type) {
	case *entity.Department:
		return createDepartmentTable(ctx, db, tableName)
	case *entity.Employee:
		return createEmployeeTable(ctx, db, tableName)
	case *entity.Role:
		return createRoleTable(ctx, db, tableName)
	case *entity.EmployeeRole:
		return createEmployeeRoleTable(ctx, db, tableName)
	default:
		// 对于其他实体类型，暂时跳过创建表，只记录日志
		glog.Infof(ctx, "跳过创建表 %s，实体类型: %T", tableName, model)
		return nil
	}
}

// createDepartmentTable 创建部门表
func createDepartmentTable(ctx context.Context, db gdb.DB, tableName string) error {
	sql := fmt.Sprintf(`
		CREATE TABLE %s (
			id INT UNSIGNED AUTO_INCREMENT PRIMARY KEY,
			company_id INT UNSIGNED NOT NULL COMMENT '公司ID',
			parent_id INT UNSIGNED DEFAULT 0 COMMENT '上级部门ID，0表示顶级部门',
			name VARCHAR(100) NOT NULL COMMENT '部门名称',
			code VARCHAR(50) NOT NULL COMMENT '部门编码',
			description TEXT COMMENT '部门描述',
			manager_id INT UNSIGNED DEFAULT 0 COMMENT '部门经理ID',
			level INT DEFAULT 1 COMMENT '部门层级',
			sort INT DEFAULT 0 COMMENT '排序',
			status TINYINT DEFAULT 1 COMMENT '状态：1-正常，0-禁用',
			created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
			updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
			deleted_at TIMESTAMP NULL COMMENT '删除时间',
			INDEX idx_company_id (company_id),
			INDEX idx_parent_id (parent_id),
			UNIQUE KEY uk_company_code (company_id, code)
		) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='部门表'
	`, tableName)

	_, err := db.Ctx(ctx).Exec(ctx, sql)
	return err
}

// createEmployeeTable 创建员工表
func createEmployeeTable(ctx context.Context, db gdb.DB, tableName string) error {
	sql := fmt.Sprintf(`
		CREATE TABLE %s (
			id INT UNSIGNED AUTO_INCREMENT PRIMARY KEY,
			company_id INT UNSIGNED NOT NULL COMMENT '公司ID',
			department_id INT UNSIGNED NOT NULL COMMENT '部门ID',
			employee_no VARCHAR(50) COMMENT '员工工号',
			username VARCHAR(50) NOT NULL COMMENT '用户名',
			password VARCHAR(255) NOT NULL COMMENT '密码',
			real_name VARCHAR(50) NOT NULL COMMENT '真实姓名',
			nickname VARCHAR(50) COMMENT '昵称',
			avatar VARCHAR(255) COMMENT '头像',
			gender TINYINT DEFAULT 0 COMMENT '性别：1-男，2-女，0-未知',
			birthday DATE COMMENT '生日',
			phone VARCHAR(20) COMMENT '手机号',
			email VARCHAR(100) COMMENT '邮箱',
			id_card VARCHAR(20) COMMENT '身份证号',
			address VARCHAR(255) COMMENT '地址',
			entry_date DATE COMMENT '入职日期',
			leave_date DATE COMMENT '离职日期',
			position VARCHAR(100) COMMENT '职位',
			level VARCHAR(50) COMMENT '职级',
			salary DECIMAL(10,2) DEFAULT 0 COMMENT '薪资',
			status TINYINT DEFAULT 1 COMMENT '状态：1-在职，2-离职，0-禁用',
			remark TEXT COMMENT '备注',
			created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
			updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
			deleted_at TIMESTAMP NULL COMMENT '删除时间',
			INDEX idx_company_id (company_id),
			INDEX idx_department_id (department_id),
			UNIQUE KEY uk_company_username (company_id, username),
			UNIQUE KEY uk_company_employee_no (company_id, employee_no)
		) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='员工表'
	`, tableName)

	_, err := db.Ctx(ctx).Exec(ctx, sql)
	return err
}

// createRoleTable 创建角色表
func createRoleTable(ctx context.Context, db gdb.DB, tableName string) error {
	sql := fmt.Sprintf(`
		CREATE TABLE %s (
			id INT UNSIGNED AUTO_INCREMENT PRIMARY KEY,
			company_id INT UNSIGNED NOT NULL COMMENT '公司ID',
			name VARCHAR(50) NOT NULL COMMENT '角色名称',
			code VARCHAR(50) NOT NULL COMMENT '角色编码',
			description TEXT COMMENT '角色描述',
			type TINYINT DEFAULT 2 COMMENT '角色类型：1-系统角色，2-自定义角色',
			level INT DEFAULT 1 COMMENT '角色级别',
			permissions TEXT COMMENT '权限列表，JSON格式',
			status TINYINT DEFAULT 1 COMMENT '状态：1-正常，0-禁用',
			created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
			updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
			deleted_at TIMESTAMP NULL COMMENT '删除时间',
			INDEX idx_company_id (company_id),
			UNIQUE KEY uk_company_code (company_id, code),
			UNIQUE KEY uk_company_name (company_id, name)
		) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='角色表'
	`, tableName)

	_, err := db.Ctx(ctx).Exec(ctx, sql)
	return err
}

// createEmployeeRoleTable 创建员工角色关联表
func createEmployeeRoleTable(ctx context.Context, db gdb.DB, tableName string) error {
	sql := fmt.Sprintf(`
		CREATE TABLE %s (
			id INT UNSIGNED AUTO_INCREMENT PRIMARY KEY,
			employee_id INT UNSIGNED NOT NULL COMMENT '员工ID',
			role_id INT UNSIGNED NOT NULL COMMENT '角色ID',
			created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
			INDEX idx_employee_id (employee_id),
			INDEX idx_role_id (role_id),
			UNIQUE KEY uk_employee_role (employee_id, role_id)
		) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='员工角色关联表'
	`, tableName)

	_, err := db.Ctx(ctx).Exec(ctx, sql)
	return err
}
