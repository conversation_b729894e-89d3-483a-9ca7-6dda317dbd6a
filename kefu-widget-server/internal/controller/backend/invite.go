package backend

import (
	"context"

	"github.com/gogf/gf/v2/frame/g"
	"github.com/gogf/gf/v2/net/ghttp"

	"kefu-server/internal/service"
	"kefu-server/internal/service/invite"
)

// InviteController 邀请控制器
type InviteController struct{}

// CreateInvite 创建邀请
func (c *InviteController) CreateInvite(r *ghttp.Request) {
	ctx := context.Background()

	var req invite.CreateInviteReq
	if err := r.Parse(&req); err != nil {
		g.Log().Error(ctx, "参数解析失败:", err)
		r.Response.WriteJsonExit(g.Map{
			"code":    400,
			"message": "参数格式错误",
			"data":    nil,
		})
		return
	}

	// 参数验证
	if err := g.Validator().Data(req).Run(ctx); err != nil {
		g.Log().Error(ctx, "参数验证失败:", err)
		r.Response.WriteJsonExit(g.Map{
			"code":    400,
			"message": err.String(),
			"data":    nil,
		})
		return
	}

	// 调用服务创建邀请
	result, err := service.Invite().CreateInvite(ctx, &req)
	if err != nil {
		g.Log().Error(ctx, "创建邀请失败:", err)
		r.Response.WriteJsonExit(g.Map{
			"code":    500,
			"message": err.Error(),
			"data":    nil,
		})
		return
	}

	r.Response.WriteJsonExit(g.Map{
		"code":    200,
		"message": "邀请创建成功",
		"data":    result,
	})
}

// AcceptInvite 接受邀请
func (c *InviteController) AcceptInvite(r *ghttp.Request) {
	ctx := context.Background()

	var req invite.AcceptInviteReq
	if err := r.Parse(&req); err != nil {
		g.Log().Error(ctx, "参数解析失败:", err)
		r.Response.WriteJsonExit(g.Map{
			"code":    400,
			"message": "参数格式错误",
			"data":    nil,
		})
		return
	}

	// 参数验证
	if err := g.Validator().Data(req).Run(ctx); err != nil {
		g.Log().Error(ctx, "参数验证失败:", err)
		r.Response.WriteJsonExit(g.Map{
			"code":    400,
			"message": err.String(),
			"data":    nil,
		})
		return
	}

	// 调用服务接受邀请
	result, err := service.Invite().AcceptInvite(ctx, &req)
	if err != nil {
		g.Log().Error(ctx, "接受邀请失败:", err)
		r.Response.WriteJsonExit(g.Map{
			"code":    500,
			"message": err.Error(),
			"data":    nil,
		})
		return
	}

	r.Response.WriteJsonExit(g.Map{
		"code":    200,
		"message": "邀请接受成功",
		"data":    result,
	})
}

// GetInviteByCode 根据邀请码获取邀请信息
func (c *InviteController) GetInviteByCode(r *ghttp.Request) {
	ctx := context.Background()

	inviteCode := r.Get("invite_code").String()
	if inviteCode == "" {
		r.Response.WriteJsonExit(g.Map{
			"code":    400,
			"message": "邀请码不能为空",
			"data":    nil,
		})
		return
	}

	req := &invite.GetInviteByCodeReq{
		InviteCode: inviteCode,
	}

	// 调用服务获取邀请信息
	result, err := service.Invite().GetInviteByCode(ctx, req)
	if err != nil {
		g.Log().Error(ctx, "获取邀请信息失败:", err)
		r.Response.WriteJsonExit(g.Map{
			"code":    500,
			"message": err.Error(),
			"data":    nil,
		})
		return
	}

	r.Response.WriteJsonExit(g.Map{
		"code":    200,
		"message": "获取成功",
		"data":    result,
	})
}

// GetInviteList 获取邀请列表
func (c *InviteController) GetInviteList(r *ghttp.Request) {
	ctx := context.Background()

	var req invite.GetInviteListReq
	if err := r.Parse(&req); err != nil {
		g.Log().Error(ctx, "参数解析失败:", err)
		r.Response.WriteJsonExit(g.Map{
			"code":    400,
			"message": "参数格式错误",
			"data":    nil,
		})
		return
	}

	// 设置默认分页参数
	if req.Page <= 0 {
		req.Page = 1
	}
	if req.Limit <= 0 {
		req.Limit = 10
	}

	// 参数验证
	if err := g.Validator().Data(req).Run(ctx); err != nil {
		g.Log().Error(ctx, "参数验证失败:", err)
		r.Response.WriteJsonExit(g.Map{
			"code":    400,
			"message": err.String(),
			"data":    nil,
		})
		return
	}

	// 调用服务获取邀请列表
	list, total, err := service.Invite().GetInviteList(ctx, &req)
	if err != nil {
		g.Log().Error(ctx, "获取邀请列表失败:", err)
		r.Response.WriteJsonExit(g.Map{
			"code":    500,
			"message": err.Error(),
			"data":    nil,
		})
		return
	}

	r.Response.WriteJsonExit(g.Map{
		"code":    200,
		"message": "获取成功",
		"data": g.Map{
			"list":  list,
			"total": total,
			"page":  req.Page,
			"limit": req.Limit,
		},
	})
}

// UpdateInviteStatus 更新邀请状态
func (c *InviteController) UpdateInviteStatus(r *ghttp.Request) {
	ctx := context.Background()

	var req invite.UpdateInviteStatusReq
	if err := r.Parse(&req); err != nil {
		g.Log().Error(ctx, "参数解析失败:", err)
		r.Response.WriteJsonExit(g.Map{
			"code":    400,
			"message": "参数格式错误",
			"data":    nil,
		})
		return
	}

	// 参数验证
	if err := g.Validator().Data(req).Run(ctx); err != nil {
		g.Log().Error(ctx, "参数验证失败:", err)
		r.Response.WriteJsonExit(g.Map{
			"code":    400,
			"message": err.String(),
			"data":    nil,
		})
		return
	}

	// 调用服务更新邀请状态
	err := service.Invite().UpdateInviteStatus(ctx, &req)
	if err != nil {
		g.Log().Error(ctx, "更新邀请状态失败:", err)
		r.Response.WriteJsonExit(g.Map{
			"code":    500,
			"message": err.Error(),
			"data":    nil,
		})
		return
	}

	r.Response.WriteJsonExit(g.Map{
		"code":    200,
		"message": "更新成功",
		"data":    nil,
	})
}

// DeleteInvite 删除邀请
func (c *InviteController) DeleteInvite(r *ghttp.Request) {
	ctx := context.Background()

	id := r.Get("id").Uint()
	companyId := r.Get("company_id").Uint()

	if id == 0 {
		r.Response.WriteJsonExit(g.Map{
			"code":    400,
			"message": "邀请记录ID不能为空",
			"data":    nil,
		})
		return
	}

	if companyId == 0 {
		r.Response.WriteJsonExit(g.Map{
			"code":    400,
			"message": "公司ID不能为空",
			"data":    nil,
		})
		return
	}

	// 调用服务删除邀请
	err := service.Invite().DeleteInvite(ctx, id, companyId)
	if err != nil {
		g.Log().Error(ctx, "删除邀请失败:", err)
		r.Response.WriteJsonExit(g.Map{
			"code":    500,
			"message": err.Error(),
			"data":    nil,
		})
		return
	}

	r.Response.WriteJsonExit(g.Map{
		"code":    200,
		"message": "删除成功",
		"data":    nil,
	})
}
