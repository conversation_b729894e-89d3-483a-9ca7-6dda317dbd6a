package backend

import (
	"context"

	"github.com/gogf/gf/v2/frame/g"
	"github.com/gogf/gf/v2/net/ghttp"

	"kefu-server/internal/service"
	"kefu-server/internal/service/invite"
)

// InviteController 邀请控制器
type InviteController struct{}

// CreateInvite 创建邀请访客会话记录
func (c *InviteController) CreateInvite(r *ghttp.Request) {
	ctx := context.Background()

	var req invite.CreateInviteReq
	if err := r.Parse(&req); err != nil {
		g.Log().Error(ctx, "参数解析失败:", err)
		r.Response.WriteJsonExit(g.Map{
			"code":    400,
			"message": "参数格式错误",
			"data":    nil,
		})
		return
	}

	// 参数验证
	if err := g.Validator().Data(req).Run(ctx); err != nil {
		g.Log().Error(ctx, "参数验证失败:", err)
		r.Response.WriteJsonExit(g.Map{
			"code":    400,
			"message": err.String(),
			"data":    nil,
		})
		return
	}

	// 调用服务创建邀请记录
	result, err := service.Invite().CreateInvite(ctx, &req)
	if err != nil {
		g.Log().Error(ctx, "创建邀请记录失败:", err)
		r.Response.WriteJsonExit(g.Map{
			"code":    500,
			"message": err.Error(),
			"data":    nil,
		})
		return
	}

	r.Response.WriteJsonExit(g.Map{
		"code":    200,
		"message": "邀请记录创建成功",
		"data":    result,
	})
}

// UpdateVisitorResponse 更新访客响应
func (c *InviteController) UpdateVisitorResponse(r *ghttp.Request) {
	ctx := context.Background()

	var req invite.UpdateVisitorResponseReq
	if err := r.Parse(&req); err != nil {
		g.Log().Error(ctx, "参数解析失败:", err)
		r.Response.WriteJsonExit(g.Map{
			"code":    400,
			"message": "参数格式错误",
			"data":    nil,
		})
		return
	}

	// 参数验证
	if err := g.Validator().Data(req).Run(ctx); err != nil {
		g.Log().Error(ctx, "参数验证失败:", err)
		r.Response.WriteJsonExit(g.Map{
			"code":    400,
			"message": err.String(),
			"data":    nil,
		})
		return
	}

	// 调用服务更新访客响应
	err := service.Invite().UpdateVisitorResponse(ctx, &req)
	if err != nil {
		g.Log().Error(ctx, "更新访客响应失败:", err)
		r.Response.WriteJsonExit(g.Map{
			"code":    500,
			"message": err.Error(),
			"data":    nil,
		})
		return
	}

	r.Response.WriteJsonExit(g.Map{
		"code":    200,
		"message": "访客响应更新成功",
		"data":    nil,
	})
}

// GetInviteStatistics 获取邀请统计
func (c *InviteController) GetInviteStatistics(r *ghttp.Request) {
	ctx := context.Background()

	var req invite.GetInviteStatisticsReq
	if err := r.Parse(&req); err != nil {
		g.Log().Error(ctx, "参数解析失败:", err)
		r.Response.WriteJsonExit(g.Map{
			"code":    400,
			"message": "参数格式错误",
			"data":    nil,
		})
		return
	}

	// 调用服务获取邀请统计
	result, err := service.Invite().GetInviteStatistics(ctx, &req)
	if err != nil {
		g.Log().Error(ctx, "获取邀请统计失败:", err)
		r.Response.WriteJsonExit(g.Map{
			"code":    500,
			"message": err.Error(),
			"data":    nil,
		})
		return
	}

	r.Response.WriteJsonExit(g.Map{
		"code":    200,
		"message": "获取成功",
		"data":    result,
	})
}

// GetInviteList 获取邀请列表
func (c *InviteController) GetInviteList(r *ghttp.Request) {
	ctx := context.Background()

	var req invite.GetInviteListReq
	if err := r.Parse(&req); err != nil {
		g.Log().Error(ctx, "参数解析失败:", err)
		r.Response.WriteJsonExit(g.Map{
			"code":    400,
			"message": "参数格式错误",
			"data":    nil,
		})
		return
	}

	// 设置默认分页参数
	if req.Page <= 0 {
		req.Page = 1
	}
	if req.Limit <= 0 {
		req.Limit = 10
	}

	// 参数验证
	if err := g.Validator().Data(req).Run(ctx); err != nil {
		g.Log().Error(ctx, "参数验证失败:", err)
		r.Response.WriteJsonExit(g.Map{
			"code":    400,
			"message": err.String(),
			"data":    nil,
		})
		return
	}

	// 调用服务获取邀请列表
	list, total, err := service.Invite().GetInviteList(ctx, &req)
	if err != nil {
		g.Log().Error(ctx, "获取邀请列表失败:", err)
		r.Response.WriteJsonExit(g.Map{
			"code":    500,
			"message": err.Error(),
			"data":    nil,
		})
		return
	}

	r.Response.WriteJsonExit(g.Map{
		"code":    200,
		"message": "获取成功",
		"data": g.Map{
			"list":  list,
			"total": total,
			"page":  req.Page,
			"limit": req.Limit,
		},
	})
}

// DeleteInvite 删除邀请记录
func (c *InviteController) DeleteInvite(r *ghttp.Request) {
	ctx := context.Background()

	id := r.Get("id").Uint()

	if id == 0 {
		r.Response.WriteJsonExit(g.Map{
			"code":    400,
			"message": "邀请记录ID不能为空",
			"data":    nil,
		})
		return
	}

	// 调用服务删除邀请记录
	err := service.Invite().DeleteInvite(ctx, id)
	if err != nil {
		g.Log().Error(ctx, "删除邀请记录失败:", err)
		r.Response.WriteJsonExit(g.Map{
			"code":    500,
			"message": err.Error(),
			"data":    nil,
		})
		return
	}

	r.Response.WriteJsonExit(g.Map{
		"code":    200,
		"message": "删除成功",
		"data":    nil,
	})
}
