package frontend

import (
	"kefu-server/internal/service"

	"github.com/gogf/gf/v2/frame/g"
	"github.com/gogf/gf/v2/net/ghttp"
)

type SiteController struct{}

func NewSiteController() *SiteController {
	return &SiteController{}
}

// GetSiteConfig 获取站点配置信息
// GET /api/frontend/site/config?site_key=xxx
func (c *SiteController) GetSiteConfig(r *ghttp.Request) {
	ctx := r.Context()
	siteKey := r.Get("site_key").String()

	if siteKey == "" {
		r.Response.WriteJsonExit(g.Map{
			"code": 400,
			"msg":  "站点密钥不能为空",
			"data": nil,
		})
	}

	// 从数据库获取站点配置
	siteConfig, err := service.Site().GetSiteConfigBySiteKey(ctx, siteKey)
	if err != nil {
		g.Log().Error(ctx, "获取站点配置失败:", err)
		r.Response.WriteJsonExit(g.Map{
			"code": 500,
			"msg":  "获取站点配置失败",
			"data": nil,
		})
		return
	}

	if siteConfig == nil {
		r.Response.WriteJsonExit(g.Map{
			"code": 404,
			"msg":  "站点不存在或已禁用",
			"data": nil,
		})
		return
	}

	r.Response.WriteJsonExit(g.Map{
		"code": 200,
		"msg":  "获取成功",
		"data": siteConfig,
	})
}
